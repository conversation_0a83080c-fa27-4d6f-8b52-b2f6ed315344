#!/usr/bin/env python3
"""
EasyOCR模型预下载脚本
将EasyOCR模型下载到本地models目录，实现离线部署
"""

import os
import shutil
import easyocr
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def download_easyocr_models_to_local():
    """将EasyOCR模型下载到本地models目录"""
    try:
        # 创建models目录
        models_dir = Path("models")
        models_dir.mkdir(exist_ok=True)
        
        # 创建EasyOCR模型子目录
        easyocr_models_dir = models_dir / "easyocr"
        easyocr_models_dir.mkdir(exist_ok=True)
        
        logger.info("开始下载EasyOCR模型到本地models目录...")
        logger.info(f"目标目录: {easyocr_models_dir.absolute()}")
        
        # 设置EasyOCR模型存储目录
        os.environ['EASYOCR_MODULE_PATH'] = str(easyocr_models_dir.absolute())
        
        # 创建EasyOCR Reader，这会自动下载模型到指定目录
        reader = easyocr.Reader(
            ['ch_sim', 'en'], 
            gpu=False, 
            verbose=True,
            model_storage_directory=str(easyocr_models_dir.absolute())
        )
        
        logger.info("✅ EasyOCR模型下载完成！")
        logger.info(f"模型保存位置: {easyocr_models_dir.absolute()}")
        
        # 列出下载的模型文件
        if easyocr_models_dir.exists():
            model_files = list(easyocr_models_dir.rglob("*"))
            logger.info(f"下载的模型文件数量: {len(model_files)}")
            for file in model_files:
                if file.is_file():
                    logger.info(f"  - {file.name} ({file.stat().st_size / 1024 / 1024:.1f} MB)")
        
        # 测试模型是否正常工作
        import numpy as np
        test_image = np.ones((100, 300, 3), dtype=np.uint8) * 255  # 创建白色测试图片
        result = reader.readtext(test_image)
        
        logger.info("✅ 模型测试成功，可以离线使用！")
        
        # 创建模型配置文件
        config_file = easyocr_models_dir / "model_info.txt"
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(f"EasyOCR模型下载信息\n")
            f.write(f"下载时间: {__import__('datetime').datetime.now()}\n")
            f.write(f"支持语言: 中文简体(ch_sim), 英文(en)\n")
            f.write(f"模型目录: {easyocr_models_dir.absolute()}\n")
            f.write(f"模型文件数量: {len(model_files)}\n")
        
        return True, str(easyocr_models_dir.absolute())
        
    except Exception as e:
        logger.error(f"❌ 模型下载失败: {e}")
        return False, None

if __name__ == "__main__":
    success, model_path = download_easyocr_models_to_local()
    if success:
        print(f"\n🎉 EasyOCR模型下载完成！")
        print(f"📁 模型保存位置: {model_path}")
        print(f"📝 现在可以使用 --ocr_model_path {model_path} 参数来指定模型路径")
        print(f"📝 或者程序会自动从 models/easyocr/ 目录加载模型")
    else:
        print("\n❌ 模型下载失败，请检查网络连接。")