#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型缓存优化模块
实现模型预加载和缓存机制，提升启动性能
"""

import pickle
import hashlib
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from ultralytics import YOLO
from paddleocr import PaddleOCR

logger = logging.getLogger(__name__)

class ModelCache:
    """模型缓存管理器"""
    
    def __init__(self, cache_dir: str = "models/cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self._yolo_cache: Dict[str, YOLO] = {}
        self._ocr_cache: Dict[str, PaddleOCR] = {}
    
    def _get_model_hash(self, model_path: str, config: Dict[str, Any] = None) -> str:
        """生成模型配置的哈希值"""
        model_stat = Path(model_path).stat()
        hash_data = f"{model_path}_{model_stat.st_mtime}_{model_stat.st_size}"
        if config:
            hash_data += str(sorted(config.items()))
        return hashlib.md5(hash_data.encode()).hexdigest()
    
    def load_yolo_model(self, model_path: str, force_reload: bool = False) -> YOLO:
        """加载YOLO模型（带缓存）"""
        if not force_reload and model_path in self._yolo_cache:
            logger.info(f"从缓存加载YOLO模型: {model_path}")
            return self._yolo_cache[model_path]
        
        logger.info(f"加载YOLO模型: {model_path}")
        model = YOLO(model_path)
        self._yolo_cache[model_path] = model
        return model
    
    def load_ocr_model(self, config: Dict[str, Any], force_reload: bool = False) -> PaddleOCR:
        """加载OCR模型（带缓存）"""
        config_key = str(sorted(config.items()))
        
        if not force_reload and config_key in self._ocr_cache:
            logger.info("从缓存加载OCR模型")
            return self._ocr_cache[config_key]
        
        logger.info("加载OCR模型")
        ocr_engine = PaddleOCR(**config)
        self._ocr_cache[config_key] = ocr_engine
        return ocr_engine
    
    def preload_models(self, yolo_paths: list, ocr_config: Dict[str, Any]):
        """预加载所有模型"""
        logger.info("开始预加载模型...")
        
        # 预加载YOLO模型
        for path in yolo_paths:
            self.load_yolo_model(path)
        
        # 预加载OCR模型
        if ocr_config:
            self.load_ocr_model(ocr_config)
        
        logger.info("模型预加载完成")
    
    def clear_cache(self):
        """清空缓存"""
        self._yolo_cache.clear()
        self._ocr_cache.clear()
        logger.info("模型缓存已清空")

# 全局模型缓存实例
model_cache = ModelCache()
