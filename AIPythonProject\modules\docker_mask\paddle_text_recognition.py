#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaddleOCR 文字识别工具
使用 PaddleOCR 2.7 对图片进行文字识别并打印结果
"""

import os
import sys
import argparse

# 检查关键依赖的导入
def check_dependencies():
    """检查关键依赖是否正确安装"""
    missing_deps = []
    
    try:
        import numpy as np
        print(f"✓ numpy {np.__version__}")
    except ImportError as e:
        missing_deps.append(("numpy", "pip install numpy==1.24.3"))
        print(f"✗ numpy 导入失败: {e}")
    
    try:
        import cv2
        print(f"✓ opencv-python {cv2.__version__}")
    except ImportError as e:
        missing_deps.append(("opencv-python", "pip install opencv-python==********"))
        print(f"✗ opencv-python 导入失败: {e}")
        if "numpy.core.multiarray" in str(e):
            print("  这通常是 numpy 和 OpenCV 版本不兼容导致的")
            print("  请运行: python fix_dependencies.py")
    
    try:
        from PIL import Image
        print("✓ Pillow")
    except ImportError as e:
        missing_deps.append(("Pillow", "pip install pillow==9.5.0"))
        print(f"✗ Pillow 导入失败: {e}")
    
    try:
        from paddleocr import PaddleOCR
        print("✓ PaddleOCR")
    except ImportError as e:
        missing_deps.append(("paddleocr", "pip install paddleocr==2.7.3"))
        print(f"✗ PaddleOCR 导入失败: {e}")
    
    if missing_deps:
        print("\n缺少以下依赖:")
        for dep_name, install_cmd in missing_deps:
            print(f"  {dep_name}: {install_cmd}")
        sys.exit(1)

# 执行依赖检查
check_dependencies()

# 导入已检查的模块
import cv2
import numpy as np
from PIL import Image
from paddleocr import PaddleOCR


class PaddleTextRecognizer:
    """PaddleOCR 文字识别器"""
    
    def __init__(self, use_textline_orientation=True, lang='ch', use_gpu=False, model_dir=None):
        """
        初始化 PaddleOCR

        Args:
            use_textline_orientation (bool): 是否使用文本行方向分类器
            lang (str): 语言类型，'ch' 为中英文，'en' 为英文
            use_gpu (bool): 是否使用GPU
            model_dir (str): 本地模型目录路径，如果为None则使用在线模型
        """
        try:
            # 设置模型路径
            ocr_params = {
                'use_textline_orientation': use_textline_orientation,
                'lang': lang,
                'use_gpu': use_gpu
            }
            
            # 如果指定了本地模型目录，则使用本地模型
            if model_dir and os.path.exists(model_dir):
                print(f"使用本地模型目录: {model_dir}")
                
                # 设置本地模型路径
                det_model_dir = os.path.join(model_dir, 'det')
                rec_model_dir = os.path.join(model_dir, 'rec')
                cls_model_dir = os.path.join(model_dir, 'cls')
                
                if os.path.exists(det_model_dir):
                    ocr_params['text_detection_model_dir'] = det_model_dir
                    print(f"检测模型路径: {det_model_dir}")

                if os.path.exists(rec_model_dir):
                    ocr_params['text_recognition_model_dir'] = rec_model_dir
                    print(f"识别模型路径: {rec_model_dir}")

                if use_textline_orientation and os.path.exists(cls_model_dir):
                    ocr_params['textline_orientation_model_dir'] = cls_model_dir
                    print(f"分类模型路径: {cls_model_dir}")
            
            self.ocr = PaddleOCR(**ocr_params)
            model_source = "本地模型" if model_dir else "在线模型"
            print(f"PaddleOCR 初始化成功 - 语言: {lang}, GPU: {use_gpu}, 模型: {model_source}")
            
        except Exception as e:
            print(f"PaddleOCR 初始化失败: {e}")
            sys.exit(1)
    
    def recognize_image(self, image_path):
        """
        识别图片中的文字
        
        Args:
            image_path (str): 图片路径
            
        Returns:
            list: 识别结果列表
        """
        if not os.path.exists(image_path):
            print(f"错误: 图片文件不存在 - {image_path}")
            return []
        
        try:
            # 使用 PaddleOCR 进行识别
            result = self.ocr.ocr(image_path, cls=True)
            return result
        except Exception as e:
            print(f"图片识别失败: {e}")
            return []
    
    def print_results(self, results, image_path):
        """
        打印识别结果
        
        Args:
            results (list): 识别结果
            image_path (str): 图片路径
        """
        print(f"\n{'='*60}")
        print(f"图片路径: {image_path}")
        print(f"{'='*60}")
        
        if not results or not results[0]:
            print("未识别到任何文字内容")
            return
        
        print(f"识别到 {len(results[0])} 个文字区域:")
        print("-" * 60)
        
        all_text = []
        for idx, line in enumerate(results[0], 1):
            if len(line) >= 2:
                # line[0] 是坐标信息，line[1] 是文字和置信度
                text_info = line[1]
                if isinstance(text_info, tuple) and len(text_info) >= 2:
                    text, confidence = text_info[0], text_info[1]
                    print(f"{idx:2d}. 文字: {text}")
                    print(f"    置信度: {confidence:.4f}")
                    print(f"    坐标: {line[0]}")
                    print("-" * 40)
                    all_text.append(text)
        
        # 打印完整文本
        if all_text:
            print(f"\n完整识别文本:")
            print("=" * 60)
            full_text = " ".join(all_text)
            print(full_text)
            print("=" * 60)
    
    def recognize_and_print(self, image_path):
        """
        识别图片并打印结果
        
        Args:
            image_path (str): 图片路径
        """
        results = self.recognize_image(image_path)
        self.print_results(results, image_path)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='PaddleOCR 图片文字识别工具')
    parser.add_argument('image_path', help='图片文件路径')
    parser.add_argument('--lang', default='ch', choices=['ch', 'en'], 
                       help='语言类型 (默认: ch)')
    parser.add_argument('--gpu', action='store_true', 
                       help='使用GPU加速')
    parser.add_argument('--no-angle', action='store_true',
                       help='不使用角度分类器')
    parser.add_argument('--model-dir', type=str,
                       help='本地模型目录路径 (例如: models/paddleocr)')
    
    args = parser.parse_args()
    
    # 检查图片文件
    if not os.path.exists(args.image_path):
        print(f"错误: 图片文件不存在 - {args.image_path}")
        return
    
    # 如果没有指定模型目录，检查默认的 models/paddleocr 目录
    model_dir = args.model_dir
    if not model_dir:
        default_model_dir = os.path.join('models', 'paddleocr')
        if os.path.exists(default_model_dir):
            model_dir = default_model_dir
            print(f"找到默认模型目录: {model_dir}")
    
    # 创建识别器
    recognizer = PaddleTextRecognizer(
        use_textline_orientation=not args.no_angle,
        lang=args.lang,
        use_gpu=args.gpu,
        model_dir=model_dir
    )
    
    # 执行识别
    recognizer.recognize_and_print(args.image_path)


if __name__ == "__main__":
    main()