#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaddleOCR 模型下载工具
下载 PaddleOCR 模型到本地 models/paddleocr 目录
"""

import os
import sys
import urllib.request
import zipfile
import tarfile
import shutil
from pathlib import Path

# PaddleOCR 2.7 官方模型下载链接
MODEL_URLS = {
    'ch': {
        'det': 'https://paddleocr.bj.bcebos.com/PP-OCRv4/chinese/ch_PP-OCRv4_det_infer.tar',
        'rec': 'https://paddleocr.bj.bcebos.com/PP-OCRv4/chinese/ch_PP-OCRv4_rec_infer.tar',
        'cls': 'https://paddleocr.bj.bcebos.com/dygraph_v2.0/ch/ch_ppocr_mobile_v2.0_cls_infer.tar'
    },
    'en': {
        'det': 'https://paddleocr.bj.bcebos.com/PP-OCRv4/english/en_PP-OCRv4_det_infer.tar',
        'rec': 'https://paddleocr.bj.bcebos.com/PP-OCRv4/english/en_PP-OCRv4_rec_infer.tar',
        'cls': 'https://paddleocr.bj.bcebos.com/dygraph_v2.0/ch/ch_ppocr_mobile_v2.0_cls_infer.tar'
    }
}

def download_file(url, local_path):
    """
    下载文件
    
    Args:
        url (str): 下载链接
        local_path (str): 本地保存路径
    """
    print(f"正在下载: {url}")
    print(f"保存到: {local_path}")
    
    try:
        # 创建目录
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        
        # 下载文件
        urllib.request.urlretrieve(url, local_path)
        print(f"下载完成: {local_path}")
        return True
    except Exception as e:
        print(f"下载失败: {e}")
        return False

def extract_tar(tar_path, extract_to):
    """
    解压 tar 文件
    
    Args:
        tar_path (str): tar 文件路径
        extract_to (str): 解压目标目录
    """
    try:
        print(f"正在解压: {tar_path}")
        with tarfile.open(tar_path, 'r') as tar:
            tar.extractall(extract_to)
        print(f"解压完成: {extract_to}")
        return True
    except Exception as e:
        print(f"解压失败: {e}")
        return False

def organize_model_files(temp_dir, target_dir, model_type):
    """
    整理模型文件到目标目录
    
    Args:
        temp_dir (str): 临时解压目录
        target_dir (str): 目标模型目录
        model_type (str): 模型类型 (det/rec/cls)
    """
    try:
        # 查找解压后的模型目录
        for item in os.listdir(temp_dir):
            item_path = os.path.join(temp_dir, item)
            if os.path.isdir(item_path) and ('infer' in item or model_type in item):
                # 移动模型文件到目标目录
                final_target = os.path.join(target_dir, model_type)
                if os.path.exists(final_target):
                    shutil.rmtree(final_target)
                shutil.move(item_path, final_target)
                print(f"模型文件已移动到: {final_target}")
                return True
        
        print(f"警告: 未找到 {model_type} 模型文件")
        return False
    except Exception as e:
        print(f"整理模型文件失败: {e}")
        return False

def download_paddleocr_models(lang='ch', models_dir='models/paddleocr'):
    """
    下载 PaddleOCR 模型
    
    Args:
        lang (str): 语言类型 ('ch' 或 'en')
        models_dir (str): 模型保存目录
    """
    print(f"开始下载 PaddleOCR {lang} 模型到 {models_dir}")
    
    # 创建模型目录
    os.makedirs(models_dir, exist_ok=True)
    temp_dir = os.path.join(models_dir, 'temp')
    os.makedirs(temp_dir, exist_ok=True)
    
    if lang not in MODEL_URLS:
        print(f"错误: 不支持的语言类型 {lang}")
        return False
    
    success_count = 0
    total_models = len(MODEL_URLS[lang])
    
    for model_type, url in MODEL_URLS[lang].items():
        print(f"\n{'='*50}")
        print(f"下载 {model_type} 模型...")
        print(f"{'='*50}")
        
        # 下载文件
        filename = url.split('/')[-1]
        local_path = os.path.join(temp_dir, filename)
        
        if download_file(url, local_path):
            # 解压文件
            extract_temp = os.path.join(temp_dir, f'{model_type}_extract')
            os.makedirs(extract_temp, exist_ok=True)
            
            if extract_tar(local_path, extract_temp):
                # 整理模型文件
                if organize_model_files(extract_temp, models_dir, model_type):
                    success_count += 1
                
                # 清理临时文件
                shutil.rmtree(extract_temp)
            
            # 删除下载的压缩文件
            os.remove(local_path)
    
    # 清理临时目录
    if os.path.exists(temp_dir):
        shutil.rmtree(temp_dir)
    
    print(f"\n{'='*50}")
    print(f"下载完成: {success_count}/{total_models} 个模型下载成功")
    print(f"模型保存在: {os.path.abspath(models_dir)}")
    print(f"{'='*50}")
    
    return success_count == total_models

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='PaddleOCR 模型下载工具')
    parser.add_argument('--lang', default='ch', choices=['ch', 'en'],
                       help='语言类型 (默认: ch)')
    parser.add_argument('--models-dir', default='models/paddleocr',
                       help='模型保存目录 (默认: models/paddleocr)')
    
    args = parser.parse_args()
    
    # 下载模型
    success = download_paddleocr_models(args.lang, args.models_dir)
    
    if success:
        print("\n所有模型下载完成！")
        print(f"现在可以使用以下命令进行文字识别:")
        print(f"python paddle_text_recognition.py your_image.jpg --model-dir {args.models_dir}")
    else:
        print("\n模型下载过程中出现错误，请检查网络连接后重试")
        sys.exit(1)

if __name__ == "__main__":
    main()