"""
图片敏感信息脱敏工具

功能：
1. YOLO目标检测和马赛克处理
2. OCR敏感文字检测和脱敏（姓名、身份证号、银行卡号、地址数字）
3. 多种脱敏方式：马赛克、模糊、噪声

命令示例：
# 仅OCR敏感文字检测
python mosaic_image.py -i input.jpg -o output.jpg --enable_ocr --ocr_mode all --no_yolo

# 指定文字脱敏方式为模糊
python mosaic_image.py -i input.jpg -o output.jpg --enable_ocr --text_method blur --no_yolo

# YOLO + OCR组合检测
python mosaic_image.py -i input.jpg -o output.jpg --yolo_models models/yolov8n.pt --enable_ocr

# 批量处理
python mosaic_image.py -i input_dir -o output_dir --enable_ocr --batch --no_yolo
"""

import argparse
import logging
import re
import sys
from pathlib import Path
from typing import List, Tuple

import cv2
import easyocr
import numpy as np
from ultralytics import YOLO

# 确保日志目录存在
log_dir = Path("models")
log_dir.mkdir(exist_ok=True)

# 配置日志系统
logging.basicConfig(
    level=logging.INFO,  # 使用 DEBUG 级别以记录详细日志
    format='%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
    handlers=[
        logging.FileHandler(log_dir / 'image_mosaic_enhanced.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class ImageProcessor:
    def __init__(self, yolo_model_paths: List[str], enable_yolo: bool = True, enable_ocr: bool = False,
                 ocr_mode: str = 'all', offline_mode: bool = False, ocr_model_path: str = None,
                 enable_id_card_fixed_regions: bool = False, enable_bank_card_fixed_regions: bool = False,
                 enable_text_merge: bool = True):
        """
        初始化检测器
        :param yolo_model_paths: 用户指定的多个 YOLOv8 模型路径列表
        :param enable_yolo: 是否启用 YOLO 检测
        :param enable_ocr: 是否启用 OCR 文字检测
        :param ocr_mode: OCR 检测模式 ('id_card', 'bank_card', 'driver_license', 'all')
        :param offline_mode: 是否启用离线模式（不下载模型）
        :param ocr_model_path: OCR模型路径，如果不指定则使用默认的models/easyocr/目录
        :param enable_id_card_fixed_regions: 是否启用身份证固定区域马赛克（针对银行卡场景）
        :param enable_bank_card_fixed_regions: 是否启用银行卡固定区域马赛克（当OCR识别不准确时）
        :param enable_text_merge: 是否启用文本段合并功能（用于合并被分割的银行卡号等）
        """
        self.enable_yolo = enable_yolo
        self.enable_ocr = enable_ocr
        self.ocr_mode = ocr_mode
        self.enable_id_card_fixed_regions = enable_id_card_fixed_regions
        self.enable_bank_card_fixed_regions = enable_bank_card_fixed_regions
        self.enable_text_merge = enable_text_merge
        self.yolo_models = []
        self.yolo_classes = {}  # 存储每个模型的类别
        self.ocr_engine = None

        # 敏感信息正则表达式模式 - 保留姓名、身份证号和银行卡识别
        self.sensitive_patterns = {
            'id_card': [
                # 标准身份证号码格式
                r'\d{17}[\dXx]',  # 18位身份证号
                r'\d{15}',  # 15位身份证号
                r'\d{6}19\d{2}\d{2}\d{2}\d{3}[\dXx]',  # 更具体的18位身份证格式
                r'\d{6}20\d{2}\d{2}\d{2}\d{3}[\dXx]',  # 2000年后出生的身份证
                r'\d{6}18\d{2}\d{2}\d{2}\d{3}[\dXx]',  # 1800年代的身份证

                # 通用容错模式：处理各种OCR识别错误
                r'\d{14,18}[\dXx]?',      # 14-18位数字，可选的X结尾
                r'\d{13,17}[\dXx]',       # 13-17位数字加X结尾

                # 处理可能的分段识别（OCR在数字中间插入分隔符）
                r'\d{6,8}\d{6,10}[\dXx]?',   # 前6-8位 + 后6-10位的组合
                r'\d{10,12}\d{4,8}[\dXx]?',  # 前10-12位 + 后4-8位的组合
                r'\d{8,10}\d{6,8}[\dXx]?',   # 前8-10位 + 后6-8位的组合

                # 处理OCR识别错误：包含标点符号的身份证号码
                r'\d{10,12}[,，.。]\d{4,8}[\dXx]?',  # 中间有逗号或句号的情况
                r'\d{8,10}[,，.。]\d{6,8}[\dXx]?',   # 不同位置的分隔符
                r'\d{6,8}[,，.。]\d{8,10}[\dXx]?',   # 前段较短的情况
                r'\d{11}[,，.。]\d{6}[\dXx]?',       # 11+6位的组合（常见OCR错误）

                # 宽松匹配：适应各种长度变化
                r'[0-9]{15,18}',          # 纯数字15-18位

                # 特殊处理：包含多个分隔符的情况
                r'\d{6,8}[,，.。]\d{4,6}[,，.。]?\d{4,6}[\dXx]?',  # 多段分割
                r'[0-9]{14,17}[Xx]',      # 14-17位数字加X
                r'\d{12,18}',             # 12-18位数字（最宽松）
            ],
            'bank_card': [
                # 标准银行卡格式（最常见的16位）- 优化空格处理
                r'\d{4}\s*\d{4}\s*\d{4}\s*\d{4}',  # 16位，支持任意空格
                r'\d{4}[\s\-]*\d{4}[\s\-]*\d{4}[\s\-]*\d{4}',  # 16位分段格式（保留原有）

                # 增强的空格容错模式 - 针对OCR识别中的空格问题
                r'\d{4}\s+\d{4}\s+\d{4}\s+\d{4}',  # 16位，必须有空格分隔
                r'\d{4}\s{1,3}\d{4}\s{1,3}\d{4}\s{1,3}\d{4}',  # 16位，1-3个空格
                r'\d{4}\s+\d{4}\s+\d{4}\s+\d{3,4}',  # 15-16位，空格分隔
                r'\d{4}\s+\d{4}\s+\d{4}\s+\d{5}',  # 17位，空格分隔
                r'\d{4}\s+\d{4}\s+\d{4}\s+\d{6}',  # 18位，空格分隔
                r'\d{4}\s+\d{4}\s+\d{4}\s+\d{7}',  # 19位，空格分隔

                # 不规则空格分布的模式
                r'\d{3,5}\s+\d{3,5}\s+\d{3,5}\s+\d{3,5}',  # 灵活长度，空格分隔
                r'\d{6}\s+\d{4}\s+\d{4}\s+\d{2,4}',  # 6-4-4-X格式
                r'\d{5}\s+\d{4}\s+\d{4}\s+\d{3,4}',  # 5-4-4-X格式

                # 处理多个连续空格的情况
                r'\d{4}\s{2,}\d{4}\s{2,}\d{4}\s{2,}\d{4}',  # 多个空格
                r'\d{4}\s{1,5}\d{4}\s{1,5}\d{4}\s{1,5}\d{3,7}',  # 1-5个空格，灵活末尾

                # 其他常见长度（保留原有）
                r'\d{15}',  # 15位连续数字
                r'\d{19}',  # 19位连续数字

                # 灵活分段格式（适应各种分隔方式）
                r'\d{6}\d{10}',  # 6-10格式连续
                r'\d{6}[\s\-]*\d{10}',  # 6-10格式分段
                r'\d{6}[\s\-]*\d{6}[\s\-]*\d{4}',  # 6-6-4格式
                r'\d{4}[\s\-]*\d{6}[\s\-]*\d{5}',  # 4-6-5格式
                r'\d{4}[\s\-]*\d{4}[\s\-]*\d{4}[\s\-]*\d{3,7}',  # 4-4-4-X格式（X为3-7位）

                # 常见银行卡BIN（Bank Identification Number）开头 - 增加空格支持
                r'4\d{3}\s*\d{4}\s*\d{4}\s*\d{4}',    # Visa卡（4开头，16位）带空格
                r'5[1-5]\d{2}\s*\d{4}\s*\d{4}\s*\d{4}',  # MasterCard（51-55开头，16位）带空格
                r'62\d{2}\s*\d{4}\s*\d{4}\s*\d{4,7}',   # 银联卡（62开头，16-19位）带空格
                r'9\d{3}\s*\d{4}\s*\d{4}\s*\d{4}',    # 其他卡种（9开头，16位）带空格

                # 原有BIN模式（保留）
                r'4\d{15}',    # Visa卡（4开头，16位）
                r'5[1-5]\d{14}',  # MasterCard（51-55开头，16位）
                r'62\d{14,17}',   # 银联卡（62开头，16-19位）
                r'9\d{15}',    # 其他卡种（9开头，16位）

                # 宽松匹配（适应OCR识别误差）
                r'\d{13,19}',  # 13-19位连续数字
                r'\d{4}[\s\-]*\d{4}[\s\-]*\d{4}[\s\-]*\d{1,7}',  # 灵活末尾长度
            ],
            'name': [
                # 英文姓名（首字母大写）
                r'[A-Z][a-z]+\s+[A-Z][a-z]+',
                r'[A-Z][a-z]+\s+[A-Z]\.\s*[A-Z][a-z]+',  # 中间名缩写
                # 常见中文姓氏开头的姓名（更精确的模式，只匹配2-3字）
                r'[王李张刘陈杨黄赵周吴徐孙朱马胡郭林何高梁郑罗宋谢唐韩曹许邓萧冯曾程蔡彭潘袁于董余苏叶吕魏蒋田杜丁沈姜范江傅钟卢汪戴崔任陆廖姚方金邱夏谭韦贾邹石熊孟秦阎薛侯雷白龙段郝孔邵史毛常万顾赖武康贺严尹钱施牛洪龚][\u4e00-\u9fff]{1,2}',
                # 复姓（更精确的模式）
                r'(欧阳|太史|端木|上官|司马|东方|独孤|南宫|万俟|闻人|夏侯|诸葛|尉迟|公羊|赫连|澹台|皇甫|宗政|濮阳|公冶|太叔|申屠|公孙|慕容|仲孙|钟离|长孙|宇文|司徒|鲜于|司空|闾丘|子车|亓官|司寇|巫马|公西|颛孙|壤驷|公良|漆雕|乐正|宰父|谷梁|拓跋|夹谷|轩辕|令狐|段干|百里|呼延|东郭|南门|羊舌|微生|公户|公玉|公仪|梁丘|公仲|公上|公门|公山|公坚|左丘|公伯|西门|公祖|第五|公乘|贯丘|公皙|南荣|东里|东宫|仲长|子书|子桑|即墨|达奚|褚师)[\u4e00-\u9fff]{1,2}'
            ],
            'address_numbers': [
                # 地址中的数字部分（门牌号等）
                r'\d+号',  # 门牌号
                r'\d+栋',  # 栋号
                r'\d+楼',  # 楼号
                r'\d+室',  # 室号
                r'\d+单元',  # 单元号
                r'\d+弄',  # 弄号
                r'\d+巷',  # 巷号
            ]
        }

        try:
            if self.enable_yolo:
                if not yolo_model_paths:
                    raise ValueError("未提供 YOLO 模型路径")
                for path in yolo_model_paths:
                    if not Path(path).is_file():
                        raise FileNotFoundError(f"YOLO 模型文件不存在: {path}")
                    model = YOLO(path)
                    self.yolo_models.append(model)
                    self.yolo_classes[path] = model.names
                    logger.info(f"YOLOv8 模型加载成功，路径: {path}, 支持类别: {model.names}")
            else:
                logger.warning("YOLO 检测已禁用，将不进行目标检测")

            if self.enable_ocr:
                logger.info("正在初始化 EasyOCR...")
                try:
                    # 确定OCR模型路径
                    if ocr_model_path:
                        model_storage_dir = ocr_model_path
                    else:
                        # 默认使用本地models/easyocr目录
                        model_storage_dir = str(Path("models/easyocr").absolute())

                    logger.info(f"OCR模型目录: {model_storage_dir}")

                    # 检查本地模型是否存在
                    local_model_exists = Path(model_storage_dir).exists() and any(Path(model_storage_dir).iterdir())

                    if local_model_exists:
                        logger.info("发现本地OCR模型，使用离线模式")
                        # 使用本地模型，禁用下载
                        self.ocr_engine = easyocr.Reader(
                            ['ch_sim', 'en'],
                            gpu=False,
                            verbose=False,
                            model_storage_directory=model_storage_dir,
                            download_enabled=False
                        )
                    elif offline_mode:
                        logger.error(f"离线模式但未找到本地模型: {model_storage_dir}")
                        raise FileNotFoundError(f"离线模式但未找到本地OCR模型: {model_storage_dir}")
                    else:
                        logger.info("未找到本地模型，将下载到指定目录")
                        # 确保目录存在
                        Path(model_storage_dir).mkdir(parents=True, exist_ok=True)
                        # 下载模型到指定目录
                        self.ocr_engine = easyocr.Reader(
                            ['ch_sim', 'en'],
                            gpu=False,
                            verbose=True,
                            model_storage_directory=model_storage_dir,
                            download_enabled=True
                        )

                    logger.info(f"EasyOCR 引擎初始化成功，检测模式: {ocr_mode}")
                    logger.info(f"模型存储位置: {self.ocr_engine.model_storage_directory}")

                except Exception as e:
                    logger.error(f"EasyOCR初始化失败: {e}")
                    raise
            else:
                logger.warning("OCR 检测已禁用，将不进行文字检测")

            logger.info(
                f"初始化完成 - YOLO 检测: {enable_yolo}, OCR 检测: {enable_ocr}, 加载模型数量: {len(self.yolo_models)}")
        except Exception as e:
            logger.error(f"初始化失败: {str(e)}", exc_info=True)
            raise

    def apply_mosaic(self, roi: np.ndarray, intensity: int = 5) -> np.ndarray:
        """增强版马赛克效果，确保完整掩盖"""
        try:
            if roi.size == 0:
                logger.warning("尝试处理空ROI")
                return roi
            h, w = roi.shape[:2]
            if h < intensity or w < intensity:
                return roi
            temp = cv2.resize(roi, (intensity, intensity), interpolation=cv2.INTER_LINEAR)
            mosaic = cv2.resize(temp, (w, h), interpolation=cv2.INTER_NEAREST)
            mosaic = cv2.GaussianBlur(mosaic, (21, 21), 0)
            mosaic = cv2.medianBlur(mosaic, 15)
            return mosaic
        except Exception as e:
            logger.error(f"马赛克处理失败: {str(e)}", exc_info=True)
            return roi

    def compute_iou(self, box1: Tuple[int, int, int, int], box2: Tuple[int, int, int, int]) -> float:
        """计算两个边界框的 IoU（交并比）"""
        x1, y1, w1, h1 = box1
        x2, y2, w2, h2 = box2

        xi1 = max(x1, x2)
        yi1 = max(y1, y2)
        xi2 = min(x1 + w1, x2 + w2)
        yi2 = min(y1 + h1, y2 + h2)

        inter_area = max(0, xi2 - xi1) * max(0, yi2 - yi1)
        box1_area = w1 * h1
        box2_area = w2 * h2
        union_area = box1_area + box2_area - inter_area

        return inter_area / union_area if union_area > 0 else 0

    def remove_duplicates(self, objects: List[Tuple[int, int, int, int, str]]) -> List[Tuple[int, int, int, int, str]]:
        """去除重复的检测框，使用 IoU 阈值"""
        if not objects:
            return []

        unique_objects = []
        iou_threshold = 0.5  # IoU 阈值，超过此值视为重复

        for obj in objects:
            x, y, w, h, label = obj
            is_duplicate = False
            for unique_obj in unique_objects:
                ux, uy, uw, uh, ulabel = unique_obj
                iou = self.compute_iou((x, y, w, h), (ux, uy, uw, uh))
                if iou > iou_threshold:
                    is_duplicate = True
                    logger.debug(
                        f"移除重复目标 - 当前: ({x}, {y}, {w}, {h}, {label}), 与: ({ux}, {uy}, {uw}, {uh}, {ulabel}), IoU: {iou:.2f}")
                    break
            if not is_duplicate:
                unique_objects.append(obj)

        return unique_objects

    def clean_ocr_text(self, text: str) -> str:
        """清理OCR识别错误 - 保留原始文本用于银行卡空格检测"""
        cleaned = text
        # 去除常见分隔符（但保留空格用于银行卡检测）
        for sep in ['-', '_', ':', ',', '.', '|', '/', '\\']:
            cleaned = cleaned.replace(sep, '')

        # OCR字符纠正（仅对长数字序列）
        if len(cleaned) >= 10 and any(c.isdigit() for c in cleaned):
            corrections = {'O': '0', 'o': '0', 'l': '1', 'I': '1', 'S': '5', 'Z': '2', 'G': '6', 'B': '8'}
            for wrong, correct in corrections.items():
                cleaned = cleaned.replace(wrong, correct)

        return cleaned

    def clean_ocr_text_for_validation(self, text: str) -> str:
        """专门用于验证的文本清理 - 移除所有分隔符包括空格"""
        cleaned = text
        # 去除所有分隔符包括空格
        for sep in [' ', '-', '_', ':', ',', '.', '|', '/', '\\']:
            cleaned = cleaned.replace(sep, '')

        # OCR字符纠正（仅对长数字序列）
        if len(cleaned) >= 10 and any(c.isdigit() for c in cleaned):
            corrections = {'O': '0', 'o': '0', 'l': '1', 'I': '1', 'S': '5', 'Z': '2', 'G': '6', 'B': '8'}
            for wrong, correct in corrections.items():
                cleaned = cleaned.replace(wrong, correct)

        return cleaned

    def validate_id_card_number(self, text: str) -> bool:
        """
        验证身份证号码的基本格式和逻辑
        提供额外的验证层，减少误识别
        """
        if not text or len(text) < 15:
            return False

        # 移除所有非数字和X字符
        clean_text = ''.join(c for c in text.upper() if c.isdigit() or c == 'X')

        # 检查长度
        if len(clean_text) not in [15, 18]:
            return False

        # 检查前6位是否为有效的地区代码（简单验证）
        if len(clean_text) >= 6:
            area_code = clean_text[:6]
            # 地区代码不应该全为0或以0开头（除了特殊情况）
            if area_code == '000000' or (area_code.startswith('00') and area_code != '000000'):
                return False

        # 18位身份证的出生日期验证
        if len(clean_text) == 18:
            try:
                year = int(clean_text[6:10])
                month = int(clean_text[10:12])
                day = int(clean_text[12:14])

                # 基本的日期范围检查
                if not (1900 <= year <= 2030):
                    return False
                if not (1 <= month <= 12):
                    return False
                if not (1 <= day <= 31):
                    return False

            except (ValueError, IndexError):
                return False

        return True

    def is_likely_id_card_loose(self, text: str) -> bool:
        """
        宽松的身份证号码检测，用于处理OCR识别错误的情况
        对于疑似身份证号码，即使格式不完全标准也要进行马赛克处理
        """
        if not text or len(text) < 13:
            return False

        # 移除所有非数字和X字符，统计数字字符数量
        digits_only = ''.join(c for c in text if c.isdigit())
        has_x = 'X' in text.upper() or 'x' in text

        # 数字字符数量应该在合理范围内
        if len(digits_only) < 13 or len(digits_only) > 18:
            return False

        # 检查是否包含明显的身份证特征
        # 1. 长度特征：总长度在15-20之间（包含可能的OCR错误字符）
        if not (15 <= len(text) <= 20):
            return False

        # 2. 数字密度：数字字符应该占大部分
        digit_ratio = len(digits_only) / len(text)
        if digit_ratio < 0.7:  # 至少70%是数字
            return False

        # 3. 检查是否包含可能的地区代码（前6位数字）
        if len(digits_only) >= 6:
            area_code = digits_only[:6]
            # 简单的地区代码合理性检查
            if area_code.isdigit():
                # 地区代码不应该全为0或以00开头
                if area_code == '000000' or area_code.startswith('00'):
                    return False

                # 地区代码的前两位应该在合理范围内（11-82）
                try:
                    province_code = int(area_code[:2])
                    if not (11 <= province_code <= 82):
                        return False
                except ValueError:
                    return False

        # 4. 如果有足够的数字，检查可能的出生年份
        if len(digits_only) >= 10:
            # 尝试提取可能的出生年份（第7-10位或第7-8位）
            possible_years = []

            # 18位身份证格式：尝试第7-10位
            if len(digits_only) >= 10:
                year_str = digits_only[6:10]
                if year_str.isdigit() and len(year_str) == 4:
                    possible_years.append(int(year_str))

            # 15位身份证格式：尝试第7-8位（19xx年代）
            if len(digits_only) >= 8:
                year_str = digits_only[6:8]
                if year_str.isdigit() and len(year_str) == 2:
                    # 假设是19xx年代
                    possible_years.append(1900 + int(year_str))

            # 检查年份是否合理
            valid_year_found = False
            for year in possible_years:
                if 1900 <= year <= 2030:
                    valid_year_found = True
                    break

            if possible_years and not valid_year_found:
                return False

        logger.info(f"🔍 宽松检测认为可能是身份证: '{text}' (数字: {len(digits_only)}, 比例: {digit_ratio:.2f})")
        return True

    def _is_likely_id_card(self, text: str) -> bool:
        """
        预检测文本是否可能是身份证号码
        用于避免被银行卡等其他模式误匹配
        """
        if not text:
            return False

        # 移除所有非数字和X字符
        clean_text = ''.join(c for c in text.upper() if c.isdigit() or c == 'X')

        # 身份证号码长度检查
        if len(clean_text) not in [15, 18]:
            return False

        # 18位身份证的特征检查
        if len(clean_text) == 18:
            # 检查前6位地区代码的合理性
            area_code = clean_text[:6]
            if not area_code.isdigit():
                return False

            # 地区代码不应该全为0或以00开头
            if area_code == '000000' or area_code.startswith('00'):
                return False

            # 检查出生年份是否合理（19xx或20xx）
            birth_year = clean_text[6:10]
            if birth_year.isdigit():
                year = int(birth_year)
                if not (1900 <= year <= 2030):
                    return False
            else:
                return False

            # 检查月份是否合理
            birth_month = clean_text[10:12]
            if birth_month.isdigit():
                month = int(birth_month)
                if not (1 <= month <= 12):
                    return False
            else:
                return False

            # 检查日期是否合理
            birth_day = clean_text[12:14]
            if birth_day.isdigit():
                day = int(birth_day)
                if not (1 <= day <= 31):
                    return False
            else:
                return False

        # 15位身份证的特征检查
        elif len(clean_text) == 15:
            # 检查前6位地区代码
            area_code = clean_text[:6]
            if not area_code.isdigit() or area_code == '000000' or area_code.startswith('00'):
                return False

            # 检查出生年份（2位，通常是19xx年代的后两位）
            birth_year = clean_text[6:8]
            if not birth_year.isdigit():
                return False

        return True

    def validate_bank_card_number(self, text: str) -> bool:
        """
        验证银行卡号的基本格式
        增强的银行卡号验证，特别处理空格和OCR识别错误
        """
        if not text:
            return False

        # 记录原始文本用于调试
        original_text = text

        # 移除所有非数字字符（包括空格、横线等）
        clean_text = ''.join(c for c in text if c.isdigit())

        # 检查长度 - 银行卡号通常为13-19位
        if len(clean_text) < 13 or len(clean_text) > 19:
            logger.debug(f"银行卡号长度不符合要求: '{original_text}' -> '{clean_text}' (长度: {len(clean_text)})")
            return False

        # 检查是否全为相同数字（明显错误）
        if len(set(clean_text)) == 1:
            logger.debug(f"银行卡号全为相同数字: '{clean_text}'")
            return False

        # 检查常见的银行卡BIN（Bank Identification Number）
        # 这些是真实的银行卡号开头，提高识别准确性
        valid_bins = [
            # Visa卡 (4开头)
            '4',
            # MasterCard (51-55开头)
            '51', '52', '53', '54', '55',
            # 银联卡 (62开头)
            '62',
            # American Express (34, 37开头)
            '34', '37',
            # Discover (6开头)
            '6011', '644', '645', '646', '647', '648', '649', '65',
            # JCB (35开头)
            '35',
            # 中国银行卡常见BIN
            '95', '96', '97', '98', '99',  # 一些中国银行的BIN
            # 其他常见BIN
            '30', '36', '38',  # Diners Club
        ]

        # 检查是否以有效BIN开头
        has_valid_bin = any(clean_text.startswith(bin_code) for bin_code in valid_bins)

        # 简化的Luhn算法验证
        def luhn_check(card_num):
            try:
                def digits_of(n):
                    return [int(d) for d in str(n)]
                digits = digits_of(card_num)
                odd_digits = digits[-1::-2]
                even_digits = digits[-2::-2]
                checksum = sum(odd_digits)
                for d in even_digits:
                    checksum += sum(digits_of(d*2))
                return checksum % 10 == 0
            except:
                return False

        luhn_valid = luhn_check(clean_text)

        # 综合判断：如果有有效BIN或通过Luhn验证，则认为是有效银行卡号
        is_valid = has_valid_bin or luhn_valid

        logger.debug(f"银行卡号验证: '{original_text}' -> '{clean_text}' | BIN有效: {has_valid_bin} | Luhn有效: {luhn_valid} | 最终结果: {is_valid}")

        return is_valid

    def detect_bank_card_by_bin(self, text: str) -> bool:
        """
        专门检测银行卡BIN码（前4-6位）
        即使不是完整的银行卡号，只要前几位匹配BIN码就认为是银行卡相关信息
        """
        if not text:
            return False

        # 移除所有非数字字符
        clean_text = ''.join(c for c in text if c.isdigit())
        
        # 至少需要4位数字才能判断BIN
        if len(clean_text) < 4:
            return False

        # 扩展的银行卡BIN码列表（包含更多中国银行的BIN）
        bank_bins = [
            # 国际卡组织
            '4',     # Visa (4开头)
            '51', '52', '53', '54', '55',  # MasterCard (51-55开头)
            '34', '37',  # American Express
            '35',    # JCB
            '30', '36', '38',  # Diners Club
            '6011', '644', '645', '646', '647', '648', '649', '65',  # Discover
            
            # 中国银联 (62开头)
            '62',
            
            # 中国主要银行BIN码
            # 工商银行
            '6222', '6225', '6226', '6227', '6228', '6229', '9558', '9559',
            '6215', '6216', '6217', '6218', '6219', '4563', '4564',
            
            # 建设银行
            '6236', '6227', '4367', '6230', '6231', '6232', '6233',
            '6234', '6235', '9555', '5187',
            
            # 农业银行
            '6228', '9559', '6229', '4043', '5145', '6216', '6217',
            
            # 中国银行
            '6013', '6217', '6223', '4563', '5264', '6216', '6218',
            '6219', '4340', '4361', '4367', '4563', '4564',
            
            # 交通银行
            '6222', '6223', '4563', '5218', '6228', '6229',
            
            # 招商银行
            '6225', '6226', '4392', '4403', '5187', '5291', '6216',
            '6217', '6218', '6219',
            
            # 中信银行
            '6217', '6218', '4442', '5187', '6228', '6229',
            
            # 光大银行
            '6226', '6227', '4563', '5145', '6216', '6217',
            
            # 民生银行
            '6226', '6227', '4563', '5187', '6216', '6217',
            
            # 华夏银行
            '6226', '6227', '4563', '5187', '6216', '6217',
            
            # 平安银行
            '6221', '6222', '4392', '5187', '6216', '6217',
            
            # 浦发银行
            '6225', '6226', '4392', '5187', '6216', '6217',
            
            # 兴业银行
            '6226', '6227', '4392', '5187', '6216', '6217',
            
            # 广发银行
            '6225', '6226', '4392', '5187', '6216', '6217',
            
            # 其他常见BIN
            '95', '96', '97', '98', '99',  # 一些地方银行
        ]

        # 检查是否以任何BIN码开头
        for bin_code in bank_bins:
            if clean_text.startswith(bin_code):
                logger.info(f"✅ 检测到银行卡BIN码: '{text}' -> '{clean_text}' 匹配BIN: {bin_code}")
                return True

        return False

    def extract_potential_id_cards(self, text: str) -> List[str]:
        """
        从文本中提取可能的身份证号码片段
        处理OCR合并文本的情况，如 '310101198610203222 200802.14-2018.02.14'
        """
        potential_ids = []

        # 使用正则表达式提取可能的身份证号码模式
        # 不使用单词边界，因为OCR合并文本可能没有明确的边界
        id_patterns = [
            r'\d{18}(?=\s|$|[^\d])',     # 18位数字，后面跟空格、结尾或非数字
            r'\d{17}[Xx](?=\s|$|[^\d])', # 17位数字+X，后面跟空格、结尾或非数字
            r'\d{15}(?=\s|$|[^\d])',     # 15位数字，后面跟空格、结尾或非数字
            r'\d{18}',                   # 简单的18位数字匹配
            r'\d{17}[Xx]',               # 简单的17位数字+X匹配
            r'\d{15}',                   # 简单的15位数字匹配
        ]

        for pattern in id_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                if len(match) >= 15:  # 至少15位才考虑
                    potential_ids.append(match)

        # 额外尝试：直接查找连续的数字序列
        # 这可以处理一些边界情况
        continuous_digits = re.findall(r'\d{15,18}', text)
        for digits in continuous_digits:
            if digits not in potential_ids:
                potential_ids.append(digits)

        # 去重并排序（按长度降序，优先处理更长的可能身份证号）
        potential_ids = list(set(potential_ids))
        potential_ids.sort(key=len, reverse=True)

        if potential_ids:
            logger.info(f"🔍 从文本 '{text}' 中提取到潜在身份证号码: {potential_ids}")

        return potential_ids

    def is_sensitive_text(self, text: str) -> Tuple[bool, str]:
        """智能检查文本是否包含敏感信息"""
        original_text = text
        # 使用通用的OCR容错清理
        text = self.clean_ocr_text(text)

        # 排除非敏感信息
        non_sensitive_texts = [
            # 身份证标题和标签
            "中华人民共和国", "居民身份证", "签发机关", "公安局", "上海市公安局",
            "PEOPLE'S REPUBLIC OF CHINA", "IDENTITY CARD",
            "男", "女", "性别", "民族", "汉", "出生", "住址", "公民身份号码",
            "有效期限", "VALID THRU", "签发日期", "DATE OF ISSUE",
            "中国", "CHINA", "公民", "CITIZEN", "民族", "NATION",
            "出生日期", "DATE OF BIRTH", "性别", "SEX", "住址", "ADDRESS",

            # 政府机构
            "公安", "公安厅", "派出所", "分局", "支队", "大队", "政府", "机关",
            "上海市公安局", "北京市公安局", "广州市公安局", "深圳市公安局",

            # 通用标识
            "姓名", "NAME", "日期", "DATE", "编号", "NO.", "NUMBER", "年", "月", "日",
            "YEAR", "MONTH", "DAY", "签名", "SIGNATURE", "照片", "PHOTO",

            # 常见的身份证内容片段（避免误识别）
            "中华", "人民", "共和", "共和国", "人民共和国", "中华人民", "人民共", "共利", "利囤",
            "中华人民共利囤", "华人", "民共", "和国", "共利囤",

            # 身份证上的固定文字和标签
            "有效期限", "签发机关", "公民身份号码", "居民身份证", "身份证",
            "VALID", "THRU", "DATE", "ISSUE", "IDENTITY", "CARD", "CITIZEN",

            # 日期相关
            "年", "月", "日", "YEAR", "MONTH", "DAY"
        ]

        for non_sensitive in non_sensitive_texts:
            if original_text == non_sensitive or text == non_sensitive:
                logger.info(f"⏭️ 跳过非敏感信息: '{original_text}'")
                return False, ""

        # 特殊检查：如果文本包含"中华人民共和国"的任何部分，直接跳过
        china_parts = ["中华", "人民", "共和", "共利", "利囤", "华人", "民共", "和国"]
        if any(part in original_text or part in text for part in china_parts):
            logger.info(f"⏭️ 跳过中华人民共和国相关文字: '{original_text}'")
            return False, ""

        # 检查是否为日期格式，如果是则跳过
        date_patterns = [
            r'\d{4}\.\d{2}\.\d{2}',  # 2008.02.14
            r'\d{4}-\d{2}-\d{2}',   # 2008-02-14
            r'\d{4}年\d{1,2}月\d{1,2}日',  # 2008年2月14日
            r'\d{4}\.\d{2}\.\d{2}-\d{4}\.\d{2}\.\d{2}',  # 日期范围
            r'\d{4}-\d{2}-\d{2}-\d{4}-\d{2}-\d{2}',     # 日期范围
        ]

        for pattern in date_patterns:
            if re.search(pattern, original_text) or re.search(pattern, text):
                logger.info(f"⏭️ 跳过日期格式: '{original_text}'")
                return False, ""

        # 身份证特殊处理：检查是否包含"姓名"、"性别"、"民族"等身份证关键字段标签
        id_card_field_labels = ["姓名:", "性别:", "民族:", "出生:", "住址:", "公民身份号码:", "签发机关:", "有效期限:"]
        for field in id_card_field_labels:
            if original_text == field:
                logger.info(f"⏭️ 跳过身份证字段标签: '{field}'")
                return False, ""

        # 更严格的姓名检测逻辑
        is_all_chinese = all('\u4e00' <= ch <= '\u9fff' for ch in text) if text else False
        if len(text) >= 2 and len(text) <= 4 and is_all_chinese:
            # 扩展的非敏感词列表，包含更多身份证常见词汇
            common_words = [
                "公安局", "公安厅", "派出所", "身份证", "住址", "出生", "性别", "民族", "有效", "期限", "签发", "机关",
                "中华", "人民", "共和", "共和国", "人民共和国", "中华人民", "人民共", "共利", "利囤",
                "华人", "民共", "和国", "公安", "上海", "北京", "广州", "深圳", "市公", "安局",
                "居民", "证件", "国籍", "汉族", "满族", "回族", "藏族", "维吾", "苗族", "彝族",
                "壮族", "布依", "朝鲜", "满洲", "侗族", "瑶族", "白族", "土家", "哈尼", "哈萨",
                "傣族", "黎族", "傈僳", "佤族", "畲族", "高山", "拉祜", "水族", "东乡", "纳西",
                "景颇", "柯尔", "土族", "达斡", "仫佬", "羌族", "布朗", "撒拉", "毛南", "仡佬",
                "锡伯", "阿昌", "普米", "塔吉", "怒族", "乌孜", "俄罗", "鄂温", "德昂", "保安",
                "裕固", "京族", "塔塔", "独龙", "鄂伦", "赫哲", "门巴", "珞巴", "基诺"
            ]
            if any(word in text for word in common_words):
                logger.info(f"⏭️ 跳过常见词汇: '{text}'")
                return False, ""

        # 更严格的姓氏检测：只有明确的姓氏开头才认为是姓名
        common_surnames = [
            "王", "李", "张", "刘", "陈", "杨", "黄", "赵", "周", "吴", "徐", "孙", "朱", "马", "胡",
            "郭", "林", "何", "高", "梁", "郑", "罗", "宋", "谢", "唐", "韩", "曹", "许", "邓", "萧",
            "冯", "曾", "程", "蔡", "彭", "潘", "袁", "于", "董", "余", "苏", "叶", "吕", "魏", "蒋",
            "田", "杜", "丁", "沈", "姜", "范", "江", "傅", "钟", "卢", "汪", "戴", "崔", "任", "陆",
            "廖", "姚", "方", "金", "邱", "夏", "谭", "韦", "贾", "邹", "石", "熊", "孟", "秦", "阎",
            "薛", "侯", "雷", "白", "龙", "段", "郝", "孔", "邵", "史", "毛", "常", "万", "顾", "赖",
            "武", "康", "贺", "严", "尹", "钱", "施", "牛", "洪", "龚"
        ]

        # 只有2-3个字符且以常见姓氏开头才认为是姓名（排除4字组合减少误判）
        if len(text) >= 2 and len(text) <= 3 and text and text[0] in common_surnames:
            # 确保后续字符也是汉字，且不包含明显的非姓名字符
            if all('\u4e00' <= ch <= '\u9fff' for ch in text):
                # 额外检查：避免包含明显的机构或地名字符
                institutional_chars = ["局", "厅", "所", "市", "省", "县", "区", "镇", "村", "街", "路", "号", "室", "楼", "层"]
                if not any(char in text for char in institutional_chars):
                    logger.info(f"✅ 检测到可能的姓名: '{text}' (姓氏: {text[0]})")
                    return True, "name"

        # 检查地址中的数字部分（仅对数字进行马赛克）
        address_number_patterns = self.sensitive_patterns['address_numbers']
        for pattern in address_number_patterns:
            try:
                if re.search(pattern, text):
                    logger.info(f"✅ 检测到地址中的数字: '{original_text}' 匹配模式: '{pattern}'")
                    return True, "address_numbers"
            except Exception as e:
                logger.error(f"地址数字正则表达式匹配错误: {pattern} - {str(e)}")

        # 特殊处理：优先检查身份证号码（避免被银行卡模式误匹配）
        # 先用更严格的身份证检测
        if self._is_likely_id_card(original_text):
            if self.validate_id_card_number(original_text):
                logger.info(f"✅ 检测到身份证号码: '{original_text}'")
                return True, "id_card"

        # 尝试从合并文本中提取身份证号码（处理OCR合并的情况）
        potential_id_cards = self.extract_potential_id_cards(original_text)
        for potential_id in potential_id_cards:
            # 对提取出的潜在身份证号码进行验证
            if self.validate_id_card_number(potential_id):
                logger.info(f"✅ 从合并文本中检测到身份证号码: '{potential_id}' (原文: '{original_text}')")
                return True, "id_card"
            elif self.is_likely_id_card_loose(potential_id):
                logger.info(f"✅ 从合并文本中检测到疑似身份证号码: '{potential_id}' (原文: '{original_text}')")
                return True, "id_card"

        # 优先检查银行卡BIN码（即使不是完整银行卡号也要处理）
        if self.detect_bank_card_by_bin(original_text):
            logger.info(f"✅ 检测到银行卡BIN码: '{original_text}'")
            return True, "bank_card"

        # 智能检测：检查姓名、身份证号和银行卡
        sensitive_checks = [
            ('name', self.sensitive_patterns['name']),  # 姓名检测
            ('id_card', self.sensitive_patterns['id_card']),  # 身份证号检测
            ('bank_card', self.sensitive_patterns['bank_card']),  # 银行卡检测
        ]

        # 按优先级检查敏感信息
        for info_type, patterns in sensitive_checks:
            for pattern in patterns:
                try:
                    if re.search(pattern, text):
                        # 对身份证和银行卡进行额外验证
                        if info_type == 'id_card':
                            # 先尝试严格验证
                            if self.validate_id_card_number(original_text):
                                logger.info(f"✅ 严格验证通过的身份证号码: '{original_text}'")
                                return True, info_type
                            # 严格验证失败，尝试宽松检测
                            elif self.is_likely_id_card_loose(original_text):
                                logger.info(f"✅ 宽松检测通过的疑似身份证号码: '{original_text}'")
                                return True, info_type
                            else:
                                continue
                        elif info_type == 'bank_card':
                            if self.validate_bank_card_number(original_text):
                                return True, info_type
                            else:
                                # 银行卡验证失败仍然标记为敏感（容错处理）
                                return True, info_type
                        else:
                            # 姓名等其他类型的敏感信息直接返回
                            return True, info_type
                except Exception as e:
                    logger.error(f"正则表达式匹配错误: {pattern} - {str(e)}")

        # 最后的宽松身份证检测：即使没有匹配到正则模式，也检查是否为疑似身份证
        if self.is_likely_id_card_loose(original_text):
            logger.info(f"✅ 最终宽松检测发现疑似身份证号码: '{original_text}'")
            return True, "id_card"

        return False, ""

    def merge_nearby_text_segments(self, ocr_results: List) -> List:
        """
        合并相邻的文本段，特别是银行卡号等被分割的数字序列
        使用更严格的距离判断，避免合并距离较远的文本
        """
        if not ocr_results:
            return ocr_results

        merged_results = []
        used_indices = set()

        for i, detection in enumerate(ocr_results):
            if i in used_indices:
                continue

            bbox = detection[0]
            text = detection[1]
            confidence = detection[2]

            # 计算当前文本框的中心点和尺寸
            x_coords = [point[0] for point in bbox]
            y_coords = [point[1] for point in bbox]
            center_x = sum(x_coords) / 4
            center_y = sum(y_coords) / 4
            width = max(x_coords) - min(x_coords)
            height = max(y_coords) - min(y_coords)

            # 检查是否为数字段（可能是银行卡号的一部分）
            clean_text = ''.join(c for c in text if c.isdigit() or c.isspace())
            is_digit_segment = len(clean_text.replace(' ', '')) >= 3 and clean_text.replace(' ', '').isdigit()

            if is_digit_segment:
                # 寻找相邻的数字段进行合并
                merged_text = text
                merged_bbox = bbox
                merged_confidence = confidence
                used_indices.add(i)
                merge_count = 0  # 限制合并次数，避免过度合并

                # 在水平方向上寻找相邻的数字段
                for j, other_detection in enumerate(ocr_results):
                    if j == i or j in used_indices or merge_count >= 2:  # 最多合并2个相邻段
                        continue

                    other_bbox = other_detection[0]
                    other_text = other_detection[1]
                    other_confidence = other_detection[2]

                    # 检查另一个文本是否也是数字段
                    other_clean_text = ''.join(c for c in other_text if c.isdigit() or c.isspace())
                    if len(other_clean_text.replace(' ', '')) < 3 or not other_clean_text.replace(' ', '').isdigit():
                        continue

                    # 计算另一个文本框的中心点
                    other_x_coords = [point[0] for point in other_bbox]
                    other_y_coords = [point[1] for point in other_bbox]
                    other_center_x = sum(other_x_coords) / 4
                    other_center_y = sum(other_y_coords) / 4
                    other_width = max(other_x_coords) - min(other_x_coords)
                    other_height = max(other_y_coords) - min(other_y_coords)

                    # 更严格的距离判断
                    y_distance = abs(center_y - other_center_y)
                    avg_height = (height + other_height) / 2
                    x_distance = abs(center_x - other_center_x)
                    avg_width = (width + other_width) / 2

                    # 严格的合并条件：
                    # 1. Y坐标差距不超过平均高度的30%（更严格）
                    # 2. X坐标距离不超过平均宽度的1.5倍（更严格）
                    # 3. 两个文本框之间的实际间隙不超过平均宽度的0.8倍

                    # 计算两个文本框之间的实际间隙
                    left_box_right = max(x_coords) if center_x < other_center_x else max(other_x_coords)
                    right_box_left = min(other_x_coords) if center_x < other_center_x else min(x_coords)
                    actual_gap = max(0, right_box_left - left_box_right)

                    # 严格的合并条件
                    y_threshold = avg_height * 0.3  # Y坐标容差降低到30%
                    x_threshold = avg_width * 1.5   # X坐标距离阈值降低到1.5倍
                    gap_threshold = avg_width * 0.8 # 间隙阈值设为平均宽度的80%

                    if (y_distance < y_threshold and
                        x_distance < x_threshold and
                        actual_gap < gap_threshold):

                        logger.info(f"🔗 合并相邻数字段: '{text}' + '{other_text}' (Y距离: {y_distance:.1f}, X距离: {x_distance:.1f}, 间隙: {actual_gap:.1f})")

                        # 按X坐标排序决定合并顺序
                        if center_x < other_center_x:
                            merged_text = f"{merged_text} {other_text}"
                        else:
                            merged_text = f"{other_text} {merged_text}"

                        # 合并边界框
                        all_x_coords = x_coords + other_x_coords
                        all_y_coords = y_coords + other_y_coords
                        merged_bbox = [
                            [min(all_x_coords), min(all_y_coords)],
                            [max(all_x_coords), min(all_y_coords)],
                            [max(all_x_coords), max(all_y_coords)],
                            [min(all_x_coords), max(all_y_coords)]
                        ]

                        # 使用较高的置信度
                        merged_confidence = max(merged_confidence, other_confidence)
                        used_indices.add(j)
                        merge_count += 1

                        # 更新中心点和尺寸用于下一次合并
                        center_x = sum([point[0] for point in merged_bbox]) / 4
                        center_y = sum([point[1] for point in merged_bbox]) / 4
                        width = max([point[0] for point in merged_bbox]) - min([point[0] for point in merged_bbox])
                        height = max([point[1] for point in merged_bbox]) - min([point[1] for point in merged_bbox])
                        x_coords = [point[0] for point in merged_bbox]
                        y_coords = [point[1] for point in merged_bbox]
                    else:
                        logger.debug(f"❌ 不合并数字段: '{text}' + '{other_text}' (Y距离: {y_distance:.1f}>{y_threshold:.1f}, X距离: {x_distance:.1f}>{x_threshold:.1f}, 间隙: {actual_gap:.1f}>{gap_threshold:.1f})")

                merged_results.append([merged_bbox, merged_text.strip(), merged_confidence])
                if merge_count > 0:
                    logger.info(f"✅ 合并后的文本: '{merged_text.strip()}' (合并了{merge_count}个段)")
            else:
                # 非数字段直接添加
                merged_results.append(detection)
                used_indices.add(i)

        logger.info(f"文本合并完成: {len(ocr_results)} -> {len(merged_results)} 个文本段")
        return merged_results

    def detect_text_regions(self, image: np.ndarray) -> List[Tuple[int, int, int, int, str]]:
        """使用EasyOCR检测图片中的敏感文字区域，支持分段文本合并"""
        if not self.enable_ocr or self.ocr_engine is None:
            return []

        results = []
        try:
            logger.info("开始EasyOCR文字检测...")
            # EasyOCR的readtext方法返回格式: [bbox, text, confidence]
            ocr_results = self.ocr_engine.readtext(image)

            logger.info(f"EasyOCR检测到 {len(ocr_results)} 行文字")

            # 根据设置决定是否合并相邻的文本段
            if self.enable_text_merge:
                logger.info("启用文本合并功能")
                merged_ocr_results = self.merge_nearby_text_segments(ocr_results)
            else:
                logger.info("禁用文本合并功能，直接使用原始OCR结果")
                merged_ocr_results = ocr_results

            # 定义明确的非敏感文本列表
            non_sensitive_texts = [
                "中华人民共和国", "居民身份证", "PEOPLE'S REPUBLIC OF CHINA",
                "IDENTITY CARD", "签发机关", "上海市公安局", "有效期限",
                "中华", "人民", "共和国", "居民", "身份证", "公安", "公安局",
                "签发", "机关", "有效", "期限", "REPUBLIC", "CHINA", "IDENTITY", "CARD"
            ]

            if merged_ocr_results:
                logger.info("=== OCR识别结果（合并后）===")
                for i, detection in enumerate(merged_ocr_results):
                    if len(detection) >= 3:
                        bbox = detection[0]  # 边界框坐标 [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
                        text = detection[1]  # 识别的文本
                        confidence = detection[2]  # 置信度

                        # 跳过明确的非敏感文本
                        if text in non_sensitive_texts or text.strip() in non_sensitive_texts:
                            continue

                        # 检查是否为敏感信息
                        is_sensitive, info_type = self.is_sensitive_text(text)

                        # 输出识别结果
                        if is_sensitive:
                            logger.info(f"✅ 敏感文字: '{text}' -> 类型: {info_type} (置信度: {confidence:.2f})")
                        else:
                            logger.info(f"📝 普通文字: '{text}' (置信度: {confidence:.2f})")

                        # 如果是敏感信息且置信度足够，添加到结果中
                        # 对于身份证号码和银行卡号码，使用较低的置信度阈值
                        confidence_threshold = 0.3 if info_type in ['id_card', 'bank_card'] else 0.5
                        if is_sensitive and confidence > confidence_threshold:
                            # 计算边界框
                            x_coords = [point[0] for point in bbox]
                            y_coords = [point[1] for point in bbox]
                            x, y = int(min(x_coords)), int(min(y_coords))
                            w = int(max(x_coords) - min(x_coords))
                            h = int(max(y_coords) - min(y_coords))

                            # 扩展边界框以确保完全覆盖
                            padding = 10
                            x = max(0, x - padding)
                            y = max(0, y - padding)
                            w += 2 * padding
                            h += 2 * padding

                            results.append((x, y, w, h, f"敏感文字_{info_type}"))

                logger.info(f"EasyOCR检测完成，发现 {len(results)} 个敏感文字区域")
            else:
                logger.warning("EasyOCR未检测到任何文字")

        except Exception as e:
            logger.error(f"EasyOCR文字检测失败: {str(e)}", exc_info=True)

        return results

    def detect_id_card_regions(self, image: np.ndarray) -> List[Tuple[int, int, int, int, str]]:
        """
        检测身份证框并返回固定区域的姓名和身份证号码位置
        针对银行卡场景，当OCR识别不够准确时使用固定区域马赛克
        """
        if not self.enable_id_card_fixed_regions:
            return []

        results = []
        try:
            logger.info("开始检测身份证框和固定区域...")

            # 首先尝试使用YOLO检测身份证框
            id_card_boxes = []
            if self.enable_yolo:
                for model_idx, model in enumerate(self.yolo_models):
                    model_path = list(self.yolo_classes.keys())[model_idx]
                    yolo_results = model(image, imgsz=640, conf=0.3)

                    for result in yolo_results:
                        for box in result.boxes:
                            x, y, x2, y2 = map(int, box.xyxy[0])
                            w, h = x2 - x, y2 - y
                            if w > 0 and h > 0:
                                cls_id = int(box.cls[0])
                                label = self.yolo_classes[model_path][cls_id]
                                conf = float(box.conf[0])

                                # 检查是否是身份证相关的类别
                                if any(keyword in label.lower() for keyword in ['id', 'card', 'identity', '身份证']):
                                    id_card_boxes.append((x, y, w, h, label, conf))
                                    logger.info(f"检测到身份证框: {label} - 位置: ({x}, {y}), 大小: ({w}, {h}), 置信度: {conf:.2f}")

            # 如果没有检测到身份证框，尝试通过图像特征检测
            if not id_card_boxes:
                logger.info("未通过YOLO检测到身份证框，尝试通过图像特征检测...")
                id_card_boxes = self._detect_id_card_by_features(image)

            # 为每个检测到的身份证框生成固定区域
            for id_card_box in id_card_boxes:
                x, y, w, h = id_card_box[:4]
                fixed_regions = self._generate_id_card_fixed_regions(x, y, w, h)
                results.extend(fixed_regions)
                logger.info(f"为身份证框生成了 {len(fixed_regions)} 个固定脱敏区域")

        except Exception as e:
            logger.error(f"身份证区域检测失败: {str(e)}", exc_info=True)

        return results

    def _detect_id_card_by_features(self, image: np.ndarray) -> List[Tuple[int, int, int, int, str, float]]:
        """
        通过图像特征检测身份证框
        当YOLO无法检测到身份证时的备用方案
        """
        results = []
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # 使用边缘检测
            edges = cv2.Canny(gray, 50, 150, apertureSize=3)

            # 查找轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # 筛选可能的身份证轮廓
            for contour in contours:
                # 计算轮廓面积
                area = cv2.contourArea(contour)
                if area < 10000:  # 面积太小，跳过
                    continue

                # 获取边界矩形
                x, y, w, h = cv2.boundingRect(contour)

                # 身份证的长宽比约为1.6:1
                aspect_ratio = w / h if h > 0 else 0
                if 1.4 <= aspect_ratio <= 1.8:
                    # 检查矩形是否足够大（可能是身份证）
                    if w > 200 and h > 120:
                        results.append((x, y, w, h, "id_card_detected", 0.7))
                        logger.info(f"通过特征检测到疑似身份证: 位置: ({x}, {y}), 大小: ({w}, {h}), 长宽比: {aspect_ratio:.2f}")

        except Exception as e:
            logger.error(f"特征检测身份证失败: {str(e)}", exc_info=True)

        return results

    def _generate_id_card_fixed_regions(self, card_x: int, card_y: int, card_w: int, card_h: int) -> List[Tuple[int, int, int, int, str]]:
        """
        根据身份证框的位置和大小，生成姓名和身份证号码的固定区域
        基于标准身份证布局的相对位置计算
        """
        regions = []
        try:
            # 标准身份证布局的相对位置（基于身份证总尺寸的比例）
            # 姓名区域：通常在左上部分
            name_x_ratio = 0.12  # 姓名X位置相对于身份证宽度的比例
            name_y_ratio = 0.25  # 姓名Y位置相对于身份证高度的比例
            name_w_ratio = 0.25  # 姓名区域宽度相对于身份证宽度的比例
            name_h_ratio = 0.08  # 姓名区域高度相对于身份证高度的比例

            # 身份证号码区域：通常在底部
            id_num_x_ratio = 0.12  # 身份证号码X位置相对于身份证宽度的比例
            id_num_y_ratio = 0.75  # 身份证号码Y位置相对于身份证高度的比例
            id_num_w_ratio = 0.45  # 身份证号码区域宽度相对于身份证宽度的比例
            id_num_h_ratio = 0.08  # 身份证号码区域高度相对于身份证高度的比例

            # 计算姓名区域的绝对坐标
            name_x = int(card_x + card_w * name_x_ratio)
            name_y = int(card_y + card_h * name_y_ratio)
            name_w = int(card_w * name_w_ratio)
            name_h = int(card_h * name_h_ratio)

            # 计算身份证号码区域的绝对坐标
            id_num_x = int(card_x + card_w * id_num_x_ratio)
            id_num_y = int(card_y + card_h * id_num_y_ratio)
            id_num_w = int(card_w * id_num_w_ratio)
            id_num_h = int(card_h * id_num_h_ratio)

            # 添加姓名区域
            regions.append((name_x, name_y, name_w, name_h, "固定区域_姓名"))
            logger.info(f"生成姓名固定区域: 位置: ({name_x}, {name_y}), 大小: ({name_w}, {name_h})")

            # 添加身份证号码区域
            regions.append((id_num_x, id_num_y, id_num_w, id_num_h, "固定区域_身份证号"))
            logger.info(f"生成身份证号码固定区域: 位置: ({id_num_x}, {id_num_y}), 大小: ({id_num_w}, {id_num_h})")

        except Exception as e:
            logger.error(f"生成固定区域失败: {str(e)}", exc_info=True)

        return regions

    def apply_text_desensitization(self, roi: np.ndarray, method: str = 'mosaic') -> np.ndarray:
        """对文字区域应用脱敏处理 - 只使用简单马赛克，避免黑条"""
        try:
            if roi.size == 0:
                logger.warning("收到空的ROI区域")
                return roi

            logger.debug(f"应用纯马赛克脱敏，ROI大小: {roi.shape}")

            # 使用最简单的马赛克方法，避免任何黑条
            h, w = roi.shape[:2]
            # 使用较大的块大小，确保完全看不清
            block_size = max(3, min(w, h) // 10)
            temp = cv2.resize(roi, (block_size, block_size), interpolation=cv2.INTER_LINEAR)
            result = cv2.resize(temp, (w, h), interpolation=cv2.INTER_NEAREST)

            return result

        except Exception as e:
            logger.error(f"文字脱敏处理失败: {str(e)}", exc_info=True)
            # 返回原始ROI，避免黑条
            return roi

    def detect_objects(self, image: np.ndarray) -> List[Tuple[int, int, int, int, str]]:
        """检测图片中的目标和敏感文字"""
        if image is None or image.size == 0:
            logger.warning("接收到空图片")
            return []
        results = []

        try:
            # YOLO目标检测
            if self.enable_yolo:
                for model_idx, model in enumerate(self.yolo_models):
                    model_path = list(self.yolo_classes.keys())[model_idx]
                    yolo_results = model(image, imgsz=320, conf=0.3)
                    for result in yolo_results:
                        for box in result.boxes:
                            x, y, x2, y2 = map(int, box.xyxy[0])
                            w, h = x2 - x, y2 - y
                            if w > 0 and h > 0:
                                cls_id = int(box.cls[0])
                                label = self.yolo_classes[model_path][cls_id]
                                conf = float(box.conf[0])
                                results.append((x, y, w, h, label))
                                logger.debug(
                                    f"模型 {model_idx} ({model_path}) - 检测到目标 - 类别: {label}, 置信度: {conf:.2f}, 位置: ({x}, {y}), 大小: ({w}, {h})")
                    logger.info(f"图片检测: 模型 {model_idx} 检测到 {len(result.boxes)} 个目标")

            # OCR敏感文字检测
            ocr_detected_bank_card = False
            if self.enable_ocr:
                text_regions = self.detect_text_regions(image)
                results.extend(text_regions)

                # 检查是否通过OCR检测到了银行卡号
                for region in text_regions:
                    if len(region) >= 5 and "敏感文字_bank_card" in region[4]:
                        ocr_detected_bank_card = True
                        logger.info("✅ OCR成功检测到银行卡号码")
                        break

            # 身份证固定区域检测（针对银行卡场景）
            if self.enable_id_card_fixed_regions:
                id_card_regions = self.detect_id_card_regions(image)
                results.extend(id_card_regions)
                logger.info(f"身份证固定区域检测完成，发现 {len(id_card_regions)} 个固定脱敏区域")

            # 去重
            results = self.remove_duplicates(results)
            logger.info(f"去重后总检测目标数: {len(results)}")

        except Exception as e:
            logger.error(f"目标检测失败: {str(e)}", exc_info=True)

        return results

    def process_image(self, input_path: str, output_path: str, mosaic_intensity: int = 5,
                      text_desensitization_method: str = 'mosaic') -> None:
        """处理单张图片并保存结果"""
        logger.info(f"开始处理图片: {input_path} -> {output_path}")
        try:
            image = cv2.imread(input_path)
            if image is None:
                raise IOError(f"无法打开图片文件: {input_path}")

            logger.info(f"图片信息 - 分辨率: {image.shape[1]}x{image.shape[0]}")
            objects = self.detect_objects(image)

            for (x, y, w, h, label) in objects:
                if x + w <= image.shape[1] and y + h <= image.shape[0]:
                    roi = image[y:y + h, x:x + w]

                    # 根据检测类型选择不同的处理方法
                    if label.startswith("敏感文字_"):
                        logger.info(
                            f"🔍 处理敏感文字: {label} - 位置: ({x}, {y}), 大小: ({w}, {h}), 方法: {text_desensitization_method}")
                        logger.info(
                            f"原始ROI信息: shape={roi.shape}, dtype={roi.dtype}, 非零像素数: {np.count_nonzero(roi)}")

                        processed_roi = self.apply_text_desensitization(roi, text_desensitization_method)

                        logger.info(
                            f"处理后ROI信息: shape={processed_roi.shape}, dtype={processed_roi.dtype}, 非零像素数: {np.count_nonzero(processed_roi)}")

                        # 检查处理结果是否为全黑
                        if np.count_nonzero(processed_roi) == 0:
                            logger.error(f"⚠️ 警告: 处理后的ROI为全黑! 使用备用马赛克处理")
                            processed_roi = self.apply_mosaic(roi, 3)

                        logger.info(
                            f"✅ 对 {label} 应用文字脱敏({text_desensitization_method}) - 位置: ({x}, {y}), 大小: ({w}, {h})")
                    elif label.startswith("固定区域_"):
                        # 对固定区域应用强化马赛克处理
                        if "身份证" in label or "姓名" in label:
                            logger.info(
                                f"🔍 处理身份证固定区域: {label} - 位置: ({x}, {y}), 大小: ({w}, {h})")
                        elif "银行卡" in label:
                            logger.info(
                                f"🔍 处理银行卡固定区域: {label} - 位置: ({x}, {y}), 大小: ({w}, {h})")
                        else:
                            logger.info(
                                f"🔍 处理固定区域: {label} - 位置: ({x}, {y}), 大小: ({w}, {h})")

                        processed_roi = self.apply_mosaic(roi, 3)  # 使用较强的马赛克强度
                        logger.info(
                            f"✅ 对 {label} 应用固定区域马赛克 - 位置: ({x}, {y}), 大小: ({w}, {h})")
                    else:
                        processed_roi = self.apply_mosaic(roi, mosaic_intensity)
                        logger.debug(f"对 {label} 应用马赛克 - 位置: ({x}, {y}), 大小: ({w}, {h})")

                    image[y:y + h, x:x + w] = processed_roi
                else:
                    logger.warning(f"检测目标超出边界: {label} at ({x},{y},{w},{h})")

            cv2.imwrite(output_path, image)
            logger.info(f"图片处理完成，保存至: {output_path}")
        except Exception as e:
            logger.error(f"图片处理异常: {str(e)}", exc_info=True)
            raise

    def process_images_batch(self, input_dir: str, output_dir: str, mosaic_intensity: int = 5,
                             text_desensitization_method: str = 'mosaic') -> None:
        """批量处理图片目录中的所有图片"""
        logger.info(f"开始批量处理图片 - 输入目录: {input_dir}, 输出目录: {output_dir}")
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        image_extensions = ('.jpg', '.jpeg', '.png', '.bmp')  # 支持的图片扩展名
        image_files = [f for f in input_path.glob('*') if f.suffix.lower() in image_extensions]

        if not image_files:
            logger.warning(f"输入目录 {input_dir} 中未找到图片文件")
            return

        logger.info(f"发现 {len(image_files)} 张图片待处理")
        for img_file in image_files:
            output_file = output_path / img_file.name
            self.process_image(str(img_file), str(output_file), mosaic_intensity, text_desensitization_method)

        logger.info(f"批量处理完成，总计处理 {len(image_files)} 张图片")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='增强版图片马赛克工具 + 证照敏感信息脱敏')
    parser.add_argument('-i', '--input', required=True, help="输入图片路径或目录")
    parser.add_argument('-o', '--output', required=True, help="输出图片路径或目录")
    parser.add_argument('--yolo_models', type=str, action='append', help="YOLOv8 模型路径，可多次指定")
    parser.add_argument('--intensity', type=int, default=5, help="马赛克强度（越小越模糊）")
    parser.add_argument('--no_yolo', action='store_false', dest='enable_yolo', help="禁用 YOLO 检测")
    parser.add_argument('--enable_ocr', action='store_true', help="启用 OCR 敏感文字检测")
    parser.add_argument('--ocr_mode', type=str, default='all',
                        choices=['id_card', 'bank_card', 'driver_license', 'phone', 'email', 'name', 'address', 'all'],
                        help="OCR 检测模式：id_card(身份证), bank_card(银行卡), driver_license(驾驶证), phone(手机号), email(邮箱), name(姓名), address(地址), all(全部)")
    parser.add_argument('--text_method', type=str, default='mosaic',
                        choices=['mosaic', 'blur', 'noise'],
                        help="文字脱敏方法：mosaic(马赛克), blur(模糊), noise(噪声)")
    parser.add_argument('--batch', action='store_true', help="启用批量处理模式（输入输出为目录）")
    parser.add_argument('--offline', action='store_true', help="离线模式，不下载模型（需要预先下载）")
    parser.add_argument('--ocr_model_path', type=str, help="OCR模型路径，默认使用models/easyocr/目录")
    parser.add_argument('--enable_id_card_fixed_regions', action='store_true',
                        help="启用身份证固定区域马赛克（针对银行卡场景，当OCR识别不够准确时使用）")
    parser.add_argument('--enable_bank_card_fixed_regions', action='store_true',
                        help="启用银行卡固定区域马赛克（当OCR识别银行卡号不准确时使用）")
    parser.add_argument('--disable_text_merge', action='store_true',
                        help="禁用文本段合并功能（避免合并距离较远的文本，但可能影响分段银行卡号的识别）")
    args = parser.parse_args()

    # 验证参数
    if not args.enable_yolo and not args.enable_ocr:
        logger.error("必须启用 YOLO 检测或 OCR 检测中的至少一个功能")
        sys.exit(1)

    if args.enable_yolo and not args.yolo_models:
        logger.error("启用 YOLO 检测时必须提供模型路径")
        sys.exit(1)

    try:
        processor = ImageProcessor(
            yolo_model_paths=args.yolo_models or [],
            enable_yolo=args.enable_yolo,
            enable_ocr=args.enable_ocr,
            ocr_mode=args.ocr_mode,
            offline_mode=args.offline,
            ocr_model_path=args.ocr_model_path,
            enable_id_card_fixed_regions=args.enable_id_card_fixed_regions,
            enable_bank_card_fixed_regions=args.enable_bank_card_fixed_regions,
            enable_text_merge=not args.disable_text_merge  # 注意这里是取反
        )
        if args.batch:
            processor.process_images_batch(
                input_dir=args.input,
                output_dir=args.output,
                mosaic_intensity=args.intensity,
                text_desensitization_method=args.text_method
            )
        else:
            processor.process_image(
                input_path=args.input,
                output_path=args.output,
                mosaic_intensity=args.intensity,
                text_desensitization_method=args.text_method
            )
    except Exception as e:
        logger.error(f"程序启动失败: {str(e)}", exc_info=True)
        sys.exit(1)
