#!/usr/bin/env python3
"""
获取本地环境的包版本，生成适合的 requirements.txt
运行方法: python get_local_versions.py [--full]
--full: 生成完整的依赖列表（包括所有间接依赖）
"""

import sys
import subprocess
import argparse

def get_package_version(package_name):
    """获取包版本"""
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', 'show', package_name],
                              capture_output=True, text=True)
        if result.returncode == 0:
            for line in result.stdout.split('\n'):
                if line.startswith('Version:'):
                    return line.split(':', 1)[1].strip()
    except:
        pass
    return None

def get_full_requirements():
    """获取完整的依赖列表"""
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', 'freeze'],
                              capture_output=True, text=True)
        if result.returncode == 0:
            return result.stdout.strip().split('\n')
    except:
        pass
    return []

# 需要检查的关键包
packages = [
    'torch',
    'torchvision',
    'ultralytics',
    'paddlepaddle',
    'paddleocr',
    'opencv-python',
    'opencv-python-headless',
    'numpy',
    'pillow',
    'tqdm',
    'easyocr',
    'omegaconf'
]

def main():
    parser = argparse.ArgumentParser(description='生成Docker部署用的requirements.txt')
    parser.add_argument('--full', action='store_true', help='生成完整依赖列表（包括间接依赖）')
    args = parser.parse_args()

    print("=== 检查本地环境包版本 ===")
    print(f"Python 版本: {sys.version.split()[0]}")
    print()

    if args.full:
        # 生成完整依赖列表
        print("生成完整依赖列表（包括所有间接依赖）...")
        all_packages = get_full_requirements()

        requirements_lines = [
            "# 完整依赖列表 - 根据本地正常运行环境生成",
            f"# Python 版本: {sys.version.split()[0]}",
            ""
        ]

        # 过滤掉一些不需要的包
        exclude_packages = {'pip', 'setuptools', 'wheel'}

        for package_line in all_packages:
            if package_line and '==' in package_line:
                package_name = package_line.split('==')[0].lower()
                if package_name not in exclude_packages:
                    requirements_lines.append(package_line)
                    print(f"✓ {package_line}")

        filename = 'requirements_full.txt'
    else:
        # 只生成核心包版本
        requirements_lines = [
            "# 根据本地正常运行环境生成的依赖版本",
            f"# Python 版本: {sys.version.split()[0]}",
            ""
        ]

        for package in packages:
            version = get_package_version(package)
            if version:
                line = f"{package}=={version}"
                print(f"✓ {line}")
                requirements_lines.append(line)
            else:
                print(f"✗ {package}: 未安装或无法获取版本")

        filename = 'requirements_local.txt'

    print(f"\n=== 生成的 {filename} 内容 ===")
    requirements_content = '\n'.join(requirements_lines)
    print(requirements_content)

    # 保存到文件
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(requirements_content)

    print(f"\n✓ 已保存到 {filename}")
    print(f"建议使用方式:")
    print(f"1. 复制 {filename} 内容到 requirements.txt")
    print(f"2. 或者直接在Dockerfile中使用: COPY {filename} requirements.txt")

if __name__ == "__main__":
    main()
