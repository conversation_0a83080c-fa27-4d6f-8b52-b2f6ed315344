#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片敏感信息脱敏工具 - PaddleOCR版本

功能：
1. YOLO目标检测和马赛克处理
2. OCR敏感文字检测和脱敏（姓名、身份证号、银行卡号、地址数字）- 使用PaddleOCR 2.7
3. 多种脱敏方式：马赛克、模糊、噪声

命令示例：
# 仅OCR敏感文字检测
python mosaic_image_paddle.py -i input.jpg -o output.jpg --enable_ocr --ocr_mode all --no_yolo

# 指定文字脱敏方式为模糊
python mosaic_image_paddle.py -i input.jpg -o output.jpg --enable_ocr --text_method blur --no_yolo

# YOLO + OCR组合检测
python mosaic_image_paddle.py -i input.jpg -o output.jpg --yolo_models models/yolov8n.pt --enable_ocr

# 批量处理
python mosaic_image_paddle.py -i input_dir -o output_dir --enable_ocr --batch --no_yolo
"""

import argparse
import logging
import re
import sys
from pathlib import Path
from typing import List, Tuple

import cv2
import numpy as np
from ultralytics import YOLO

# 检查PaddleOCR依赖
try:
    from paddleocr import PaddleOCR
    PADDLEOCR_AVAILABLE = True
except ImportError:
    PADDLEOCR_AVAILABLE = False
    print("警告: PaddleOCR未安装，请运行: pip install paddleocr==2.7.3")

# 确保日志目录存在
log_dir = Path("models")
log_dir.mkdir(exist_ok=True)

# 配置东八区时区
import datetime
import pytz

class BeijingFormatter(logging.Formatter):
    """自定义格式化器，使用北京时间"""
    def __init__(self, fmt=None, datefmt=None):
        super().__init__(fmt, datefmt)
        self.beijing_tz = pytz.timezone('Asia/Shanghai')

    def formatTime(self, record, datefmt=None):
        # 将UTC时间转换为北京时间
        dt = datetime.datetime.fromtimestamp(record.created, tz=self.beijing_tz)
        if datefmt:
            return dt.strftime(datefmt)
        else:
            return dt.strftime('%Y-%m-%d %H:%M:%S')

# 配置日志系统 - 使用北京时间
beijing_formatter = BeijingFormatter('[%(asctime)s] [%(levelname)8s] %(filename)s:%(lineno)d - %(message)s')

# 创建处理器
file_handler = logging.FileHandler(log_dir / 'image_mosaic_paddle.log', encoding='utf-8')
console_handler = logging.StreamHandler(sys.stdout)

# 设置格式化器
file_handler.setFormatter(beijing_formatter)
console_handler.setFormatter(beijing_formatter)

# 配置根日志器
logging.basicConfig(
    level=logging.INFO,
    handlers=[file_handler, console_handler],
    force=True
)
logger = logging.getLogger(__name__)


class ImageProcessor:
    def __init__(self, yolo_model_paths: List[str], enable_yolo: bool = True, enable_ocr: bool = False,
                 ocr_mode: str = 'all', offline_mode: bool = False, ocr_model_path: str = None,
                 enable_id_card_fixed_regions: bool = False, enable_bank_card_fixed_regions: bool = False,
                 enable_text_merge: bool = True):
        """
        初始化检测器 - PaddleOCR版本
        """
        # 简化初始化日志

        self.enable_yolo = enable_yolo
        self.enable_ocr = enable_ocr
        self.ocr_mode = ocr_mode
        self.enable_id_card_fixed_regions = enable_id_card_fixed_regions
        self.enable_bank_card_fixed_regions = enable_bank_card_fixed_regions
        self.enable_text_merge = enable_text_merge
        self.yolo_models = []
        self.yolo_classes = {}
        self.ocr_engine = None

        # 敏感信息正则表达式模式 - 优化身份证识别
        self.sensitive_patterns = {
            'id_card': [
                # 标准18位身份证号（最后一位可能是X）
                r'\b\d{17}[\dXx]\b',
                # 15位身份证号
                r'\b\d{15}\b',
                # 容错模式：17位数字+X/x
                r'\b\d{17}[Xx]\b',
                # 容错模式：包含空格或分隔符的身份证号
                r'\b\d{6}[\s\-]?\d{8}[\s\-]?\d{3}[\dXx]\b',
                r'\b\d{6}[\s\-]?\d{4}[\s\-]?\d{2}[\s\-]?\d{2}[\s\-]?\d{3}[\dXx]\b',
                # 宽松模式：14-18位数字，可能以X结尾
                r'\b\d{14,17}[\dXx]\b',
                # 超宽松模式：连续的长数字串（用于OCR识别错误的情况）
                r'\b\d{15,18}\b',
            ],
            'bank_card': [
                r'\d{4}\s*\d{4}\s*\d{4}\s*\d{4}',
                r'\d{4}[\s\-]*\d{4}[\s\-]*\d{4}[\s\-]*\d{4}',
                r'\d{15}',
                r'\d{19}',
                r'4\d{15}',
                r'5[1-5]\d{14}',
                r'62\d{14,17}',
                r'\d{13,19}',
            ],
            'name': [
                # 中文姓名模式：姓氏 + 1-3个中文字符，总长度不超过4个字
                r'[王昊李张刘陈杨黄赵周吴徐孙朱马胡郭林何高梁郑罗宋谢唐韩曹许邓萧冯曾程蔡彭潘袁于董余苏叶吕魏蒋田杜丁沈姜范江傅钟卢汪戴崔任陆廖姚方金邱夏谭韦贾邹石熊孟秦阎薛侯雷白龙段郝孔邵史毛常万顾赖武康贺严尹钱施牛洪龚巩史习郎苏谭焦萧燕邢卢祖翟樊韩谷崔赵胡娄谢瞿符皮祁危解郁卜李尹舒弓邵龙盛褚干屈雷时何田蒋支闵钟凌司王詹杭冯明牧高陆罗霍金云阮宋杜贺宁廖石鲍孙巴方蔡窦毕段裘胥费柏姚康庞和项苗奚湛白乐邹伏郭庾万季常施阎董米潘颜秦管程华贾计张文龚童君俞吕杨洪束严柯茅古劳范殷祝申宫付宣路卞聂孟向余喻毛叶乔成骆袁包马廉印元狄傅齐孔裴曹倪韦隗江姜仇纪薛强乌富景禹贡栾熊莫易尤鲁席滕邱刁章宗蓝陶山荣麻刘经戚丁徐伍滑溥艾侯房穆戴车水贝臧周黄汪幸葛昝武柴陈夏应柳梁邬贲甘许于卫沈林扶虞缪邓郑任彭梅安吴巫][\u4e00-\u9fff]{1,3}',
            ],
            'address_numbers': [
                r'\d+号',
                r'\d+栋',
                r'\d+楼',
                r'\d+室',
                r'\d+单元',
                r'\d+弄',
                r'\d+巷',
            ]
        }

        try:
            # 初始化YOLO模型
            if self.enable_yolo:
                if not yolo_model_paths:
                    raise ValueError("未提供 YOLO 模型路径")
                for path in yolo_model_paths:
                    if not Path(path).is_file():
                        raise FileNotFoundError(f"YOLO 模型文件不存在: {path}")
                    model = YOLO(path)
                    self.yolo_models.append(model)
                    self.yolo_classes[path] = model.names

            # 初始化PaddleOCR
            if self.enable_ocr:
                if not PADDLEOCR_AVAILABLE:
                    raise ImportError("PaddleOCR未安装，请运行: pip install paddleocr==2.7.3")

                try:
                    # 确定OCR模型路径
                    if ocr_model_path:
                        model_storage_dir = ocr_model_path
                    else:
                        model_storage_dir = str(Path("models/paddleocr").absolute())

                    logger.info("🔍 步骤2: 检查本地模型目录...")

                    # 检查本地模型目录是否存在
                    model_path = Path(model_storage_dir)
                    logger.info(f"检查路径: {model_path}")
                    logger.info(f"路径存在: {model_path.exists()}")

                    if model_path.exists():
                        logger.info("✅ 发现本地模型目录")
                        det_dir = model_path / 'det'
                        rec_dir = model_path / 'rec'
                        cls_dir = model_path / 'cls'

                        logger.info(f"检查子目录: det={det_dir.exists()}, rec={rec_dir.exists()}, cls={cls_dir.exists()}")

                        if det_dir.exists():
                            logger.info(f"✅ 检测模型目录存在: {det_dir}")
                        if rec_dir.exists():
                            logger.info(f"✅ 识别模型目录存在: {rec_dir}")
                        if cls_dir.exists():
                            logger.info(f"✅ 分类模型目录存在: {cls_dir}")
                    else:
                        logger.warning(f"⚠️  本地模型目录不存在: {model_storage_dir}")
                        logger.info("将使用 PaddleOCR 默认模型")

                    logger.info("✅ 步骤2完成: 模型目录检查完毕")

                    # 初始化PaddleOCR - 简化版本
                    logger.info("🔄 开始初始化 PaddleOCR 引擎...")

                    # 使用最简单可靠的参数
                    simple_params = {
                        'use_textline_orientation': True,
                        'lang': 'ch',
                        'use_gpu': False
                    }

                    logger.info(f"� 使用参数: {simple_params}")
                    # 强制使用本地模型
                    model_path = Path(model_storage_dir)
                    det_dir = model_path / 'det'
                    rec_dir = model_path / 'rec'
                    cls_dir = model_path / 'cls'

                    # 验证本地模型目录存在
                    if not det_dir.exists():
                        raise FileNotFoundError(f"检测模型目录不存在: {det_dir}")
                    if not rec_dir.exists():
                        raise FileNotFoundError(f"识别模型目录不存在: {rec_dir}")
                    if not cls_dir.exists():
                        raise FileNotFoundError(f"分类模型目录不存在: {cls_dir}")

                    logger.info("✅ 本地模型目录验证通过")
                    logger.info("🔍 步骤3.2: 准备 PaddleOCR 参数...")

                    # 使用本地模型参数
                    local_params = {
                        'use_textline_orientation': True,
                        'lang': 'ch',
                        'use_gpu': False,
                        'text_detection_model_dir': str(det_dir),
                        'text_recognition_model_dir': str(rec_dir),
                        'textline_orientation_model_dir': str(cls_dir)
                    }

                    logger.info(f"使用本地模型参数: {local_params}")
                    logger.info("🔍 步骤3.3: 开始调用 PaddleOCR() 构造函数...")
                    logger.info("⚠️  注意: 这一步可能需要较长时间，请耐心等待...")

                    # 这里是关键的初始化步骤，可能会卡住
                    self.ocr_engine = PaddleOCR(**local_params)

                    logger.info("✅ PaddleOCR() 构造函数调用成功!")
                    logger.info(f"✅ PaddleOCR 引擎初始化成功，检测模式: {ocr_mode}")

                except Exception as e:
                    logger.error(f"PaddleOCR初始化失败: {e}")
                    raise
            else:
                logger.warning("⚠️  OCR 检测已禁用，将不进行文字检测")

            logger.info("=" * 60)
            logger.info("✅ ImageProcessor 初始化完成!")
            logger.info(f"🎯 YOLO 检测: {'启用' if enable_yolo else '禁用'}")
            logger.info(f"📝 OCR 检测: {'启用' if enable_ocr else '禁用'}")
            logger.info(f"🔧 加载的 YOLO 模型数量: {len(self.yolo_models)}")
            if enable_ocr:
                logger.info(f"📝 OCR 检测模式: {ocr_mode}")
            logger.info("=" * 60)

        except Exception as e:
            logger.error(f"❌ ImageProcessor 初始化失败: {str(e)}", exc_info=True)
            raise

    def apply_mosaic(self, roi: np.ndarray, intensity: int = 5) -> np.ndarray:
        """增强版马赛克效果，确保完整掩盖"""
        try:
            if roi.size == 0:
                logger.warning("尝试处理空ROI")
                return roi
            h, w = roi.shape[:2]
            if h < intensity or w < intensity:
                return roi
            temp = cv2.resize(roi, (intensity, intensity), interpolation=cv2.INTER_LINEAR)
            mosaic = cv2.resize(temp, (w, h), interpolation=cv2.INTER_NEAREST)
            mosaic = cv2.GaussianBlur(mosaic, (21, 21), 0)
            mosaic = cv2.medianBlur(mosaic, 15)
            return mosaic
        except Exception as e:
            logger.error(f"马赛克处理失败: {str(e)}", exc_info=True)
            return roi

    def apply_blur(self, roi: np.ndarray, intensity: int = 15) -> np.ndarray:
        """应用模糊效果"""
        try:
            if roi.size == 0:
                return roi
            kernel_size = max(intensity, 5)
            if kernel_size % 2 == 0:
                kernel_size += 1
            blurred = cv2.GaussianBlur(roi, (kernel_size, kernel_size), 0)
            return blurred
        except Exception as e:
            logger.error(f"模糊处理失败: {str(e)}", exc_info=True)
            return roi

    def apply_noise(self, roi: np.ndarray, intensity: int = 50) -> np.ndarray:
        """应用噪声效果"""
        try:
            if roi.size == 0:
                return roi
            noise = np.random.randint(-intensity, intensity, roi.shape, dtype=np.int16)
            noisy = roi.astype(np.int16) + noise
            noisy = np.clip(noisy, 0, 255).astype(np.uint8)
            return noisy
        except Exception as e:
            logger.error(f"噪声处理失败: {str(e)}", exc_info=True)
            return roi

    def compute_iou(self, box1: Tuple[int, int, int, int], box2: Tuple[int, int, int, int]) -> float:
        """计算两个边界框的 IoU（交并比）"""
        x1, y1, w1, h1 = box1
        x2, y2, w2, h2 = box2

        xi1 = max(x1, x2)
        yi1 = max(y1, y2)
        xi2 = min(x1 + w1, x2 + w2)
        yi2 = min(y1 + h1, y2 + h2)

        inter_area = max(0, xi2 - xi1) * max(0, yi2 - yi1)
        box1_area = w1 * h1
        box2_area = w2 * h2
        union_area = box1_area + box2_area - inter_area

        return inter_area / union_area if union_area > 0 else 0

    def remove_duplicates(self, objects: List[Tuple[int, int, int, int, str]]) -> List[Tuple[int, int, int, int, str]]:
        """去除重复的检测框，使用 IoU 阈值"""
        if not objects:
            return []

        unique_objects = []
        iou_threshold = 0.5

        for obj in objects:
            x, y, w, h, label = obj
            is_duplicate = False
            for unique_obj in unique_objects:
                ux, uy, uw, uh, ulabel = unique_obj
                iou = self.compute_iou((x, y, w, h), (ux, uy, uw, uh))
                if iou > iou_threshold:
                    is_duplicate = True
                    logger.debug(f"移除重复目标 - 当前: ({x}, {y}, {w}, {h}, {label}), 与: ({ux}, {uy}, {uw}, {uh}, {ulabel}), IoU: {iou:.2f}")
                    break
            if not is_duplicate:
                unique_objects.append(obj)

        return unique_objects

    def clean_ocr_text(self, text: str) -> str:
        """清理OCR识别错误"""
        cleaned = text
        for sep in ['-', '_', ':', ',', '.', '|', '/', '\\']:
            cleaned = cleaned.replace(sep, '')

        if len(cleaned) >= 10 and any(c.isdigit() for c in cleaned):
            corrections = {'O': '0', 'o': '0', 'l': '1', 'I': '1', 'S': '5', 'Z': '2', 'G': '6', 'B': '8'}
            for wrong, correct in corrections.items():
                cleaned = cleaned.replace(wrong, correct)

        return cleaned

    def validate_id_card_number(self, text: str) -> bool:
        """验证身份证号码 - 简化版本，移除过度校验"""
        if not text:
            return False

        # 清理文本：只保留数字和X
        clean_text = ''.join(c for c in text.upper() if c.isdigit() or c == 'X')

        # 只检查长度：15位或18位
        if len(clean_text) not in [15, 18]:
            return False

        # 只检查基本格式，不做复杂验证
        if len(clean_text) == 18:
            # 18位身份证：前17位必须是数字，最后一位可以是数字或X
            return clean_text[:17].isdigit() and (clean_text[17].isdigit() or clean_text[17] == 'X')
        elif len(clean_text) == 15:
            # 15位身份证：全部必须是数字
            return clean_text.isdigit()

        return False

    def validate_bank_card_number(self, text: str) -> bool:
        """验证银行卡号的基本格式"""
        if not text:
            return False

        clean_text = ''.join(c for c in text if c.isdigit())

        if len(clean_text) < 13 or len(clean_text) > 19:
            return False

        if len(set(clean_text)) == 1:
            return False

        valid_bins = ['4', '51', '52', '53', '54', '55', '62', '34', '37', '35', '95', '96', '97', '98', '99', '30', '36', '38']

        has_valid_bin = any(clean_text.startswith(bin_code) for bin_code in valid_bins)

        def luhn_check(card_num):
            try:
                def digits_of(n):
                    return [int(d) for d in str(n)]
                digits = digits_of(card_num)
                odd_digits = digits[-1::-2]
                even_digits = digits[-2::-2]
                checksum = sum(odd_digits)
                for d in even_digits:
                    checksum += sum(digits_of(d*2))
                return checksum % 10 == 0
            except:
                return False

        luhn_valid = luhn_check(clean_text)
        is_valid = has_valid_bin or luhn_valid

        return is_valid

    def validate_name(self, text: str) -> bool:
        """验证姓名格式：长度不超过4个字符，且符合中文姓名规律"""
        if not text:
            return False

        # 去除空格和标点符号
        clean_text = ''.join(c for c in text if '\u4e00' <= c <= '\u9fff')

        # 检查长度：1-4个中文字符
        if len(clean_text) < 1 or len(clean_text) > 4:
            return False

        # 检查是否全是中文字符
        if not all('\u4e00' <= c <= '\u9fff' for c in clean_text):
            return False

        # 排除明显的非姓名词汇
        non_name_keywords = [
            # 证件相关
            "准驾车型", "车型", "驾驶", "证件", "身份证", "驾照", "护照",
            # 机构相关
            "公安局", "安局公安", "派出所", "政府", "机关", "部门", "单位",
            "公安", "局公", "安局", "出所", "所派",
            # 地址相关
            "街道", "社区", "村委", "居委", "小区", "大厦", "广场", "中心",
            # 其他常见非姓名词汇
            "有效期", "签发", "机关", "日期", "年月", "到期", "长期",
            "男性", "女性", "性别", "民族", "汉族", "出生", "住址"
        ]

        # 检查是否包含非姓名关键词
        for keyword in non_name_keywords:
            if keyword in clean_text:
                return False

        # 正则表达式已经检查了姓氏，这里直接返回True
        return True

    def is_sensitive_text(self, text: str) -> Tuple[bool, str]:
        """智能检查文本是否包含敏感信息"""
        original_text = text
        text = self.clean_ocr_text(text)

        non_sensitive_texts = [
            "中华人民共和国", "居民身份证", "签发机关", "公安局","安局公安"
            "PEOPLE'S REPUBLIC OF CHINA", "IDENTITY CARD",
            "男", "女", "性别", "民族", "汉", "出生", "住址", "公民身份号码",
            "有效期限", "VALID THRU", "签发日期", "DATE OF ISSUE",
            "中国", "CHINA", "公民", "CITIZEN", "民族", "NATION",
            "出生日期", "DATE OF BIRTH", "性别", "SEX", "住址", "ADDRESS",
            "公安", "公安厅", "派出所", "分局", "支队", "大队", "政府", "机关",
            "姓名", "NAME", "日期", "DATE", "编号", "NO.", "NUMBER", "年", "月", "日",
            "YEAR", "MONTH", "DAY", "签名", "SIGNATURE", "照片", "PHOTO",
            "中华", "人民", "共和", "共和国", "人民共和国", "中华人民", "人民共", "共利", "利囤",
            "有效期限", "签发机关", "公民身份号码", "居民身份证", "身份证",
            "VALID", "THRU", "DATE", "ISSUE", "IDENTITY", "CARD", "CITIZEN",
        ]

        for non_sensitive in non_sensitive_texts:
            if original_text == non_sensitive or text == non_sensitive:
                logger.info(f"⏭️ 跳过非敏感信息: '{original_text}'")
                return False, ""

        china_parts = ["中华", "人民", "共和", "共利", "利囤", "华人", "民共", "和国"]
        if any(part in original_text or part in text for part in china_parts):
            logger.info(f"⏭️ 跳过中华人民共和国相关文字: '{original_text}'")
            return False, ""

        date_patterns = [
            r'\d{4}\.\d{2}\.\d{2}',
            r'\d{4}-\d{2}-\d{2}',
            r'\d{4}年\d{1,2}月\d{1,2}日',
            r'\d{4}\.\d{2}\.\d{2}-\d{4}\.\d{2}\.\d{2}',
            r'\d{4}-\d{2}-\d{2}-\d{4}-\d{2}-\d{2}',
        ]

        for pattern in date_patterns:
            if re.search(pattern, original_text) or re.search(pattern, text):
                logger.info(f"⏭️ 跳过日期格式: '{original_text}'")
                return False, ""

        if self.ocr_mode == 'all':
            patterns_to_check = ['id_card', 'bank_card', 'name', 'address_numbers']
        elif self.ocr_mode == 'id_card':
            patterns_to_check = ['id_card', 'name']
        elif self.ocr_mode == 'bank_card':
            patterns_to_check = ['bank_card']
        elif self.ocr_mode == 'driver_license':
            patterns_to_check = ['id_card', 'name', 'address_numbers']
        else:
            patterns_to_check = ['id_card', 'bank_card', 'name', 'address_numbers']

        for pattern_type in patterns_to_check:
            if pattern_type in self.sensitive_patterns:
                for pattern in self.sensitive_patterns[pattern_type]:
                    for check_text in [original_text, text]:
                        if re.search(pattern, check_text, re.IGNORECASE):
                            if pattern_type == 'id_card':
                                if self.validate_id_card_number(check_text):
                                    logger.info(f"🔍 检测到敏感信息 ({pattern_type}): '{original_text}' -> 匹配模式: {pattern}")
                                    return True, pattern_type
                            elif pattern_type == 'bank_card':
                                if self.validate_bank_card_number(check_text):
                                    logger.info(f"🔍 检测到敏感信息 ({pattern_type}): '{original_text}' -> 匹配模式: {pattern}")
                                    return True, pattern_type
                            elif pattern_type == 'name':
                                if self.validate_name(check_text):
                                    logger.info(f"🔍 检测到敏感信息 ({pattern_type}): '{original_text}' -> 匹配模式: {pattern}")
                                    return True, pattern_type
                            else:
                                logger.info(f"🔍 检测到敏感信息 ({pattern_type}): '{original_text}' -> 匹配模式: {pattern}")
                                return True, pattern_type

        return False, ""

    def detect_text_with_paddleocr(self, image: np.ndarray) -> List[Tuple[int, int, int, int, str]]:
        """使用PaddleOCR检测图片中的文字"""
        if not self.enable_ocr or self.ocr_engine is None:
            return []

        try:
            results = self.ocr_engine.ocr(image, cls=True)

            if not results or not results[0]:
                return []

            detected_objects = []

            for i, line in enumerate(results[0]):
                if len(line) >= 2:
                    coords = line[0]
                    text_info = line[1]

                    if isinstance(text_info, tuple) and len(text_info) >= 2:
                        text, confidence = text_info[0], text_info[1]

                        x_coords = [point[0] for point in coords]
                        y_coords = [point[1] for point in coords]

                        x = int(min(x_coords))
                        y = int(min(y_coords))
                        w = int(max(x_coords) - min(x_coords))
                        h = int(max(y_coords) - min(y_coords))

                        # 核心日志：识别的文字
                        logger.info(f"识别文字: '{text}' (置信度: {confidence:.3f})")

                        # 特别调试：如果包含身份证号特征，详细记录
                        if len(text) >= 15 and any(c.isdigit() for c in text):
                            logger.info(f"长数字串检测: '{text}' (长度: {len(text)})")

                        is_sensitive, sensitive_type = self.is_sensitive_text(text)

                        if is_sensitive:
                            # 核心日志：敏感文字类型
                            logger.info(f"敏感文字: '{text}' -> 类型: {sensitive_type}")
                            detected_objects.append((x, y, w, h, f"sensitive_text_{sensitive_type}"))
                        else:
                            # 调试：记录未识别为敏感的长数字串
                            if len(text) >= 15 and any(c.isdigit() for c in text):
                                logger.info(f"未识别为敏感: '{text}' (可能是身份证号但未通过验证)")

            # 核心日志：敏感文字统计
            if detected_objects:
                logger.info(f"发现 {len(detected_objects)} 个敏感文字需要脱敏")

            return detected_objects

        except Exception as e:
            logger.error(f"PaddleOCR文字检测失败: {str(e)}", exc_info=True)
            return []

    def detect_yolo_objects(self, image: np.ndarray) -> List[Tuple[int, int, int, int, str]]:
        """使用YOLO检测目标对象"""
        if not self.enable_yolo or not self.yolo_models:
            return []

        detected_objects = []
        
        try:
            for i, model in enumerate(self.yolo_models):
                model_path = list(self.yolo_classes.keys())[i]
                logger.info(f"使用YOLO模型进行检测: {model_path}")
                
                results = model(image, verbose=False)
                
                for result in results:
                    boxes = result.boxes
                    if boxes is not None:
                        for box in boxes:
                            x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                            x, y, w, h = int(x1), int(y1), int(x2 - x1), int(y2 - y1)
                            
                            cls_id = int(box.cls[0])
                            confidence = float(box.conf[0])
                            class_name = self.yolo_classes[model_path][cls_id]
                            
                            logger.info(f"🎯 YOLO检测到目标: {class_name} (置信度: {confidence:.4f}, 位置: ({x}, {y}, {w}, {h}))")
                            detected_objects.append((x, y, w, h, f"yolo_{class_name}"))

            logger.info(f"YOLO检测完成，共发现 {len(detected_objects)} 个目标")
            return detected_objects

        except Exception as e:
            logger.error(f"YOLO目标检测失败: {str(e)}", exc_info=True)
            return []

    def process_image(self, image_path: str, output_path: str,
                     yolo_method: str = 'mosaic', text_method: str = 'mosaic',
                     yolo_intensity: int = 5, text_intensity: int = 5) -> bool:
        """处理单张图片"""
        try:
            if not Path(image_path).exists():
                logger.error(f"输入图片不存在: {image_path}")
                return False

            image = cv2.imread(image_path)
            if image is None:
                logger.error(f"无法读取图片: {image_path}")
                return False

            # 核心日志：开始处理
            logger.info(f"开始处理: {Path(image_path).name}")

            all_objects = []

            if self.enable_yolo:
                yolo_objects = self.detect_yolo_objects(image)
                all_objects.extend(yolo_objects)

            if self.enable_ocr:
                text_objects = self.detect_text_with_paddleocr(image)
                all_objects.extend(text_objects)

            unique_objects = self.remove_duplicates(all_objects)

            processed_count = 0
            for i, (x, y, w, h, label) in enumerate(unique_objects):
                try:
                    x = max(0, min(x, image.shape[1] - 1))
                    y = max(0, min(y, image.shape[0] - 1))
                    w = min(w, image.shape[1] - x)
                    h = min(h, image.shape[0] - y)

                    if w <= 0 or h <= 0:
                        continue

                    roi = image[y:y+h, x:x+w]

                    if label.startswith('yolo_'):
                        method = yolo_method
                        intensity = yolo_intensity
                    else:
                        method = text_method
                        intensity = text_intensity

                    if method == 'mosaic':
                        processed_roi = self.apply_mosaic(roi, intensity)
                    elif method == 'blur':
                        processed_roi = self.apply_blur(roi, intensity)
                    elif method == 'noise':
                        processed_roi = self.apply_noise(roi, intensity)
                    else:
                        processed_roi = self.apply_mosaic(roi, intensity)

                    image[y:y+h, x:x+w] = processed_roi
                    processed_count += 1

                    # 核心日志：脱敏成功
                    logger.info(f"脱敏成功: {label} 使用 {method} 方法")

                except Exception as e:
                    logger.error(f"处理区域失败 ({x}, {y}, {w}, {h}): {str(e)}")
                    continue

            # 保存处理后的图片
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)
            success = cv2.imwrite(output_path, image)

            if success:
                # 核心日志：处理完成
                logger.info(f"处理完成: {Path(output_path).name} (脱敏 {processed_count} 个区域)")
                return True
            else:
                logger.error(f"保存失败: {output_path}")
                return False

        except Exception as e:
            logger.error(f"处理图片失败: {str(e)}", exc_info=True)
            return False

    def process_batch(self, input_dir: str, output_dir: str,
                     yolo_method: str = 'mosaic', text_method: str = 'mosaic',
                     yolo_intensity: int = 5, text_intensity: int = 5) -> bool:
        """批量处理图片"""
        try:
            input_path = Path(input_dir)
            output_path = Path(output_dir)
            
            if not input_path.exists():
                logger.error(f"输入目录不存在: {input_dir}")
                return False

            output_path.mkdir(parents=True, exist_ok=True)

            image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}

            image_files = set()  # 使用集合避免重复
            for ext in image_extensions:
                image_files.update(input_path.glob(f'*{ext}'))
                image_files.update(input_path.glob(f'*{ext.upper()}'))

            image_files = list(image_files)  # 转换回列表

            if not image_files:
                logger.warning(f"在目录 {input_dir} 中未找到图片文件")
                return False

            logger.info(f"开始批量处理，共找到 {len(image_files)} 个图片文件")

            success_count = 0
            for image_file in image_files:
                output_file = output_path / image_file.name
                logger.info(f"处理文件 {success_count + 1}/{len(image_files)}: {image_file.name}")
                
                if self.process_image(str(image_file), str(output_file), 
                                    yolo_method, text_method, yolo_intensity, text_intensity):
                    success_count += 1
                else:
                    logger.error(f"处理失败: {image_file.name}")

            logger.info(f"批量处理完成: {success_count}/{len(image_files)} 个文件处理成功")
            return success_count > 0

        except Exception as e:
            logger.error(f"批量处理失败: {str(e)}", exc_info=True)
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='图片敏感信息脱敏工具 - PaddleOCR版本')
    
    parser.add_argument('-i', '--input', required=True, help='输入图片路径或目录')
    parser.add_argument('-o', '--output', required=True, help='输出图片路径或目录')
    parser.add_argument('--batch', action='store_true', help='批量处理模式')
    
    parser.add_argument('--yolo_models', nargs='+', default=['models/yolov8n-face.pt'],
                       help='YOLO模型路径列表')
    parser.add_argument('-target', '--target', nargs='+',
                       help='YOLO模型目标名称列表（不包含.pt扩展名）')
    parser.add_argument('--no_yolo', action='store_true', help='禁用YOLO检测')
    parser.add_argument('--yolo_method', choices=['mosaic', 'blur', 'noise'], default='mosaic',
                       help='YOLO检测目标的处理方法')
    parser.add_argument('--yolo_intensity', type=int, default=5, help='YOLO处理强度')
    
    parser.add_argument('--enable_ocr', action='store_true', help='启用OCR文字检测')
    parser.add_argument('--ocr_mode', choices=['all', 'id_card', 'bank_card', 'driver_license'], 
                       default='all', help='OCR检测模式')
    parser.add_argument('--text_method', choices=['mosaic', 'blur', 'noise'], default='mosaic',
                       help='文字的处理方法')
    parser.add_argument('--text_intensity', type=int, default=5, help='文字处理强度')
    parser.add_argument('--ocr_model_path', type=str, help='PaddleOCR模型路径')
    parser.add_argument('--offline_mode', action='store_true', help='离线模式（不下载模型）')
    
    parser.add_argument('--enable_id_card_fixed_regions', action='store_true',
                       help='启用身份证固定区域马赛克')
    parser.add_argument('--enable_bank_card_fixed_regions', action='store_true',
                       help='启用银行卡固定区域马赛克')
    parser.add_argument('--enable_text_merge', action='store_true', default=True,
                       help='启用文本段合并功能')
    
    args = parser.parse_args()

    # 处理YOLO模型路径
    if args.target and not args.no_yolo:
        # 根据target参数构建模型路径
        args.yolo_models = []
        for target in args.target:
            model_path = f"models/{target}.pt"
            args.yolo_models.append(model_path)
        logger.info(f"根据target参数构建模型路径: {args.yolo_models}")
    elif args.yolo_models:
        logger.info(f"使用指定的模型路径: {args.yolo_models}")
    elif not args.no_yolo and not args.yolo_models:
        # 使用默认模型
        logger.info(f"使用默认YOLO模型: {args.yolo_models}")

    # 简化启动信息
    print(f"PaddleOCR 脱敏工具启动")
    print(f"输入: {Path(args.input).name}")
    print(f"输出: {Path(args.output).name}")
    if not args.no_yolo:
        print(f"YOLO模型: {', '.join([Path(p).stem for p in args.yolo_models])}")
    if args.enable_ocr:
        print(f"OCR模式: {args.ocr_mode}")
    sys.stdout.flush()

    if not args.enable_ocr and args.no_yolo:
        print("❌ 错误: 不能同时禁用YOLO和OCR检测")
        logger.error("错误: 不能同时禁用YOLO和OCR检测")
        return
    
    if args.enable_ocr and not PADDLEOCR_AVAILABLE:
        logger.error("错误: PaddleOCR未安装，请运行: pip install paddleocr==2.7.3")
        return
    
    input_path = Path(args.input)
    if not input_path.exists():
        logger.error(f"输入路径不存在: {args.input}")
        return
    
    if args.batch:
        if not input_path.is_dir():
            logger.error("批量处理模式需要输入目录")
            return
    else:
        if not input_path.is_file():
            logger.error("单文件处理模式需要输入文件")
            return
    
    # 打印启动信息
    logger.info("=" * 80)
    logger.info("🚀 PaddleOCR 图片敏感信息脱敏工具启动")
    logger.info("=" * 80)
    logger.info(f"📁 输入路径: {args.input}")
    logger.info(f"📁 输出路径: {args.output}")
    logger.info(f"🔧 处理模式: {'批量处理' if args.batch else '单文件处理'}")
    logger.info(f"🎯 YOLO检测: {'禁用' if args.no_yolo else '启用'}")
    logger.info(f"📝 OCR检测: {'启用' if args.enable_ocr else '禁用'}")
    if args.enable_ocr:
        logger.info(f"📝 OCR模式: {args.ocr_mode}")
        logger.info(f"📝 文字脱敏方法: {args.text_method}")
        logger.info(f"📝 文字脱敏强度: {args.text_intensity}")
    logger.info("=" * 80)

    try:
        logger.info("🔧 初始化图片处理器...")
        processor = ImageProcessor(
            yolo_model_paths=[] if args.no_yolo else args.yolo_models,
            enable_yolo=not args.no_yolo,
            enable_ocr=args.enable_ocr,
            ocr_mode=args.ocr_mode,
            offline_mode=args.offline_mode,
            ocr_model_path=args.ocr_model_path,
            enable_id_card_fixed_regions=args.enable_id_card_fixed_regions,
            enable_bank_card_fixed_regions=args.enable_bank_card_fixed_regions,
            enable_text_merge=args.enable_text_merge
        )
        logger.info("✅ 图片处理器初始化完成")
        
        if args.batch:
            success = processor.process_batch(
                args.input, args.output,
                args.yolo_method, args.text_method,
                args.yolo_intensity, args.text_intensity
            )
        else:
            success = processor.process_image(
                args.input, args.output,
                args.yolo_method, args.text_method,
                args.yolo_intensity, args.text_intensity
            )
        
        if success:
            print("处理完成")
        else:
            print("处理失败")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()