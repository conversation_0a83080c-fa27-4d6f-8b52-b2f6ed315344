#!/bin/bash

# Docker 马赛克处理工具入口脚本

set -e

# 显示帮助信息
show_help() {
    echo "Docker 马赛克处理工具"
    echo ""
    echo "必需参数:"
    echo "  -type <类型>     处理类型: image 或 video"
    echo "  -input <路径>    输入文件或目录路径"
    echo "  -output <路径>   输出文件或目录路径"
    echo ""
    echo "可选参数:"
    echo "  -target <模型>   YOLO模型: yolov8n-face, yolov8_license_plate"
    echo "  --enable-ocr     启用OCR检测"
    echo "  --ocr-mode <模式> OCR模式: all, id_card, bank_card"
    echo "  --ocr-interval <数值> OCR检测帧间隔(默认15，数值越小检测越频繁)"
    echo "  --output-format <格式> 输出格式: mp4(默认), avi, mov, mkv, same, auto"
    echo "  --batch          批量处理"
    echo "  --no-yolo        禁用YOLO"
    echo "  --help           显示帮助"
    echo ""
    echo "示例:"
    echo "  -type image -input /app/data/input.jpg -output /app/data/output.jpg -target yolov8n-face"
    echo "  -type image -input /app/data/input.jpg -output /app/data/output.jpg --enable-ocr --no-yolo"
    echo "  -type video -input /app/data/input.mp4 -output /app/data/output.mp4 --enable-ocr --ocr-interval 10"
    echo "  -type video -input /app/data/input.avi -output /app/data/output.mp4 --output-format mp4"
    echo "  -type video -input /app/data/input.mov -output /app/data/output.mov --output-format same"
}

# 解析参数
TYPE=""
INPUT=""
OUTPUT=""
ARGS=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -type)
            TYPE="$2"
            shift 2
            ;;
        -input)
            INPUT="$2"
            shift 2
            ;;
        -output)
            OUTPUT="$2"
            shift 2
            ;;
        -target)
            ARGS="$ARGS --yolo_models models/$2.pt"
            shift 2
            ;;
        --enable-ocr)
            ARGS="$ARGS --enable_ocr"
            shift
            ;;
        --ocr-mode)
            ARGS="$ARGS --ocr_mode $2"
            shift 2
            ;;
        --ocr-interval)
            ARGS="$ARGS --ocr_interval $2"
            shift 2
            ;;
        --output-format)
            ARGS="$ARGS --output_format $2"
            shift 2
            ;;
        --batch)
            ARGS="$ARGS --batch"
            shift
            ;;
        --no-yolo)
            ARGS="$ARGS --no_yolo"
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            exit 1
            ;;
    esac
done

# 检查必要参数
if [ -z "$TYPE" ] || [ -z "$INPUT" ] || [ -z "$OUTPUT" ]; then
    echo "错误: 缺少必要参数 -type -input -output"
    show_help
    exit 1
fi

# 验证类型
if [ "$TYPE" != "image" ] && [ "$TYPE" != "video" ]; then
    echo "错误: 类型必须是 image 或 video"
    exit 1
fi

# 创建输出目录
mkdir -p "$(dirname "$OUTPUT")"

# 显示执行信息
echo "处理: $INPUT -> $OUTPUT ($TYPE)"

# 根据类型调用对应的Python脚本
if [ "$TYPE" = "image" ]; then
    python mosaic_image_paddle.py -i "$INPUT" -o "$OUTPUT" $ARGS
elif [ "$TYPE" = "video" ]; then
    python mosaic_video_paddle.py -i "$INPUT" -o "$OUTPUT" $ARGS
fi

echo "处理完成: $OUTPUT"