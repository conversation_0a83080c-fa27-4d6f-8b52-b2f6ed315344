"""
增强版视频马赛克处理工具 + 证照敏感信息脱敏
功能：
1. 支持多个指定 YOLOv8 .pt 文件进行目标检测
2. 证照敏感文字检测和脱敏（身份证、银行卡、驾驶证等）
3. 实时进度查询接口
4. 处理完成回调机制
5. 增强的日志系统
6. 多种脱敏方式：马赛克、黑条、模糊
7. OCR帧间隔优化，提高视频处理性能

支持的敏感信息类型：
- 身份证号码（15位/18位）
- 银行卡号码（15位/16位/19位）
- 驾驶证号码
- 手机号码
- 邮箱地址

命令示例：
# 单文件处理 - 仅YOLO检测
python mosaic_video.py -i input.mp4 -o output.mp4 --yolo_models models/yolov8n-face.pt

# 单文件处理 - 仅OCR敏感文字检测
python mosaic_video.py -i input.mp4 -o output.mp4 --enable_ocr --ocr_mode all

# 单文件处理 - YOLO + OCR组合检测
python mosaic_video.py -i input.mp4 -o output.mp4 --yolo_models models/yolov8n-face.pt --enable_ocr --ocr_mode id_card

# 单文件处理 - 指定文字脱敏方式和OCR间隔
python mosaic_video.py -i input.mp4 -o output.mp4 --enable_ocr --text_method black_bar --ocr_interval 15

# 批量处理 - 处理整个目录的视频文件
python mosaic_video.py -i input_folder -o output_folder --batch --yolo_models models/yolov8n-face.pt

# 批量处理 - 仅OCR检测
python mosaic_video.py -i input_folder -o output_folder --batch --enable_ocr --ocr_mode all --no_yolo

# 批量处理 - YOLO + OCR组合
python mosaic_video.py -i input_folder -o output_folder --batch --yolo_models models/yolov8n-face.pt --enable_ocr

"""

import argparse
import logging
import sys
import time
import re
from pathlib import Path
from typing import Optional, Callable, Dict, List, Tuple

import cv2
import numpy as np
from ultralytics import YOLO
from paddleocr import PaddleOCR

# 确保日志目录存在
log_dir = Path("models")
log_dir.mkdir(exist_ok=True)

# 配置日志系统
logging.basicConfig(
    level=logging.INFO,  # 使用 DEBUG 级别以记录详细日志
    format='%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
    handlers=[
        logging.FileHandler(log_dir / 'video_mosaic_enhanced.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class VideoProcessor:
    def __init__(
            self,
            yolo_model_paths: List[str],
            enable_yolo: bool = True,
            enable_ocr: bool = False,
            ocr_mode: str = 'all',
            ocr_frame_interval: int = 30
    ):
        """
        初始化检测器
        :param yolo_model_paths: 用户指定的多个 YOLOv8 模型路径列表
        :param enable_yolo: 是否启用 YOLO 检测
        :param enable_ocr: 是否启用 OCR 文字检测
        :param ocr_mode: OCR 检测模式 ('id_card', 'bank_card', 'driver_license', 'all')
        :param ocr_frame_interval: OCR检测帧间隔（每N帧检测一次，提高性能）
        """
        self._current_frame = 0
        self._total_frames = 0
        self._processing = False
        self._start_time = 0.0

        self.enable_yolo = enable_yolo
        self.enable_ocr = enable_ocr
        self.ocr_mode = ocr_mode
        self.ocr_frame_interval = ocr_frame_interval
        self.yolo_models = []
        self.yolo_classes = {}  # 存储每个模型的类别
        self.ocr_engine = None
        self._last_ocr_results = []  # 缓存OCR结果

        # 敏感信息正则表达式模式
        self.sensitive_patterns = {
            'id_card': [
                r'\d{17}[\dXx]',  # 18位身份证号
                r'\d{15}',        # 15位身份证号
            ],
            'bank_card': [
                r'\d{4}\s*\d{4}\s*\d{4}\s*\d{4}',  # 16位银行卡号
                r'\d{4}\s*\d{6}\s*\d{5}',          # 15位银行卡号
                r'\d{19}',                          # 19位银行卡号
            ],
            'driver_license': [
                r'[A-Z]\d{12}',   # 驾驶证号码格式
                r'\d{12}',        # 12位数字驾驶证
            ],
            'phone': [
                r'1[3-9]\d{9}',   # 手机号码
            ],
            'email': [
                r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',  # 邮箱
            ]
        }

        try:
            if self.enable_yolo:
                if not yolo_model_paths:
                    raise ValueError("未提供 YOLO 模型路径")
                for path in yolo_model_paths:
                    if not Path(path).is_file():
                        raise FileNotFoundError(f"YOLO 模型文件不存在: {path}")
                    model = YOLO(path)
                    self.yolo_models.append(model)
                    self.yolo_classes[path] = model.names  # 记录每个模型的类别
                    logger.info(f"YOLOv8 模型加载成功，路径: {path}, 支持类别: {model.names}")
            else:
                logger.warning("YOLO 检测已禁用，将不进行目标检测")

            if self.enable_ocr:
                logger.info("正在初始化 PaddleOCR...")
                self.ocr_engine = PaddleOCR(use_textline_orientation=True, lang='ch')
                logger.info(f"OCR 引擎初始化成功，检测模式: {ocr_mode}, 帧间隔: {ocr_frame_interval}")
            else:
                logger.warning("OCR 检测已禁用，将不进行文字检测")

            logger.info(f"初始化完成 - YOLO 检测: {enable_yolo}, OCR 检测: {enable_ocr}, 加载模型数量: {len(self.yolo_models)}")
        except Exception as e:
            logger.error(f"初始化失败: {str(e)}", exc_info=True)
            raise

    @property
    def progress(self) -> Dict[str, float]:
        if not self._processing or self._total_frames == 0:
            return {"progress": 0.0, "elapsed": 0.0, "remaining": 0.0}
        elapsed = time.time() - self._start_time
        progress = min(1.0, self._current_frame / self._total_frames)
        remaining = elapsed / progress * (1 - progress) if progress > 0 else 0.0
        return {
            "progress": round(progress, 4),
            "elapsed": round(elapsed, 2),
            "remaining": round(remaining, 2)
        }

    def apply_mosaic(self, roi: np.ndarray, intensity: int = 5) -> np.ndarray:
        """增强版马赛克效果，确保完整掩盖"""
        try:
            if roi.size == 0:
                logger.warning("尝试处理空ROI")
                return roi
            h, w = roi.shape[:2]
            if h < intensity or w < intensity:
                return roi
            # 缩小到极低分辨率，确保细节丢失
            temp = cv2.resize(roi, (intensity, intensity), interpolation=cv2.INTER_LINEAR)
            mosaic = cv2.resize(temp, (w, h), interpolation=cv2.INTER_NEAREST)
            # 多重模糊处理，确保完全掩盖
            mosaic = cv2.GaussianBlur(mosaic, (21, 21), 0)
            mosaic = cv2.medianBlur(mosaic, 15)
            return mosaic
        except Exception as e:
            logger.error(f"马赛克处理失败: {str(e)}", exc_info=True)
            return roi

    def apply_text_desensitization(self, roi: np.ndarray, method: str = 'mosaic') -> np.ndarray:
        """对文字区域应用脱敏处理"""
        try:
            if roi.size == 0:
                return roi
            
            if method == 'mosaic':
                return self.apply_mosaic(roi, intensity=3)  # 文字用更细的马赛克
            elif method == 'black_bar':
                return np.zeros_like(roi)  # 黑条遮挡
            elif method == 'blur':
                return cv2.GaussianBlur(roi, (15, 15), 0)  # 高斯模糊
            else:
                return self.apply_mosaic(roi, intensity=3)
        except Exception as e:
            logger.error(f"文字脱敏处理失败: {str(e)}", exc_info=True)
            return roi

    def is_sensitive_text(self, text: str) -> Tuple[bool, str]:
        """检查文本是否包含敏感信息"""
        text = text.replace(' ', '').replace('-', '').replace('_', '')  # 去除常见分隔符
        
        patterns_to_check = []
        if self.ocr_mode == 'all':
            for patterns in self.sensitive_patterns.values():
                patterns_to_check.extend(patterns)
        elif self.ocr_mode in self.sensitive_patterns:
            patterns_to_check = self.sensitive_patterns[self.ocr_mode]
        
        for pattern in patterns_to_check:
            if re.search(pattern, text):
                # 确定敏感信息类型
                for info_type, type_patterns in self.sensitive_patterns.items():
                    if pattern in type_patterns:
                        logger.debug(f"检测到敏感信息: {info_type} - {text}")
                        return True, info_type
        
        return False, ""

    def detect_text_regions(self, frame: np.ndarray) -> List[Tuple[int, int, int, int, str]]:
        """使用OCR检测帧中的敏感文字区域"""
        if not self.enable_ocr or self.ocr_engine is None:
            return []
        
        results = []
        try:
            ocr_results = self.ocr_engine.predict(frame)
            
            if ocr_results and ocr_results[0]:
                for line in ocr_results[0]:
                    if line and len(line) >= 2:
                        bbox = line[0]  # 边界框坐标
                        text_info = line[1]  # (文本, 置信度)
                        
                        if text_info and len(text_info) >= 2:
                            text = text_info[0]
                            confidence = text_info[1]
                            
                            # 检查是否为敏感信息
                            is_sensitive, info_type = self.is_sensitive_text(text)
                            
                            if is_sensitive and confidence > 0.5:  # 置信度阈值
                                # 计算边界框
                                x_coords = [point[0] for point in bbox]
                                y_coords = [point[1] for point in bbox]
                                x, y = int(min(x_coords)), int(min(y_coords))
                                w = int(max(x_coords) - min(x_coords))
                                h = int(max(y_coords) - min(y_coords))
                                
                                # 扩展边界框以确保完全覆盖
                                padding = 5
                                x = max(0, x - padding)
                                y = max(0, y - padding)
                                w += 2 * padding
                                h += 2 * padding
                                
                                results.append((x, y, w, h, f"敏感文字_{info_type}"))
                                logger.debug(f"帧 {self._current_frame}: 检测到敏感文字: {text} ({info_type}) - 位置: ({x}, {y}), 大小: ({w}, {h}), 置信度: {confidence:.2f}")
                
                logger.debug(f"帧 {self._current_frame}: OCR检测完成，发现 {len(results)} 个敏感文字区域")
        except Exception as e:
            logger.error(f"OCR文字检测失败: {str(e)}", exc_info=True)
        
        return results

    def detect_objects(self, frame: np.ndarray) -> list:
        """检测帧中的目标和敏感文字"""
        if frame is None or frame.size == 0:
            logger.warning("接收到空帧")
            return []
        results = []
        
        try:
            # YOLO目标检测
            if self.enable_yolo:
                for model_idx, model in enumerate(self.yolo_models):
                    model_path = list(self.yolo_classes.keys())[model_idx]  # 获取模型路径
                    yolo_results = model(frame, imgsz=320, conf=0.3)  # 动态检测所有目标
                    for result in yolo_results:
                        for box in result.boxes:
                            x, y, x2, y2 = map(int, box.xyxy[0])
                            w, h = x2 - x, y2 - y
                            if w > 0 and h > 0:
                                cls_id = int(box.cls[0])
                                label = self.yolo_classes[model_path][cls_id]
                                conf = float(box.conf[0])
                                results.append((x, y, w, h, label))
                                logger.debug(
                                    f"模型 {model_idx} ({model_path}) - 检测到目标 - 类别: {label}, 置信度: {conf:.2f}, 位置: ({x}, {y}), 大小: ({w}, {h})")
                        logger.debug(f"帧 {self._current_frame}: 模型 {model_idx} 检测到 {len(result.boxes)} 个目标")
            
            # OCR敏感文字检测（按帧间隔执行以提高性能）
            if self.enable_ocr and self._current_frame % self.ocr_frame_interval == 0:
                text_regions = self.detect_text_regions(frame)
                results.extend(text_regions)
                self._last_ocr_results = text_regions  # 缓存OCR结果
                logger.debug(f"帧 {self._current_frame}: 执行OCR检测，发现 {len(text_regions)} 个敏感文字区域")
            elif self.enable_ocr and self._last_ocr_results:
                # 使用缓存的OCR结果
                results.extend(self._last_ocr_results)
                logger.debug(f"帧 {self._current_frame}: 使用缓存的OCR结果，共 {len(self._last_ocr_results)} 个敏感文字区域")
                
        except Exception as e:
            logger.error(f"目标检测失败: {str(e)}", exc_info=True)
        
        return results

    def process_video(
            self,
            input_path: str,
            output_path: str,
            skip_frames: int = 0,
            mosaic_intensity: int = 5,
            text_desensitization_method: str = 'mosaic',
            progress_callback: Optional[Callable] = None,
            completion_callback: Optional[Callable] = None
    ) -> None:
        logger.info(f"开始处理视频: {input_path} -> {output_path}")
        self._processing = True
        self._start_time = time.time()

        cap = None
        out = None
        try:
            input_path = str(Path(input_path).resolve())
            output_path = str(Path(output_path).resolve())
            cap = cv2.VideoCapture(input_path)
            if not cap.isOpened():
                raise IOError(f"无法打开视频文件: {input_path}")

            self._total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            logger.info(f"视频信息 - 帧数: {self._total_frames}, FPS: {fps}, 分辨率: {width}x{height}")

            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
            if not out.isOpened():
                raise IOError(f"无法创建输出文件: {output_path}")

            last_objects = []
            while self._processing:
                ret, frame = cap.read()
                if not ret:
                    logger.info(f"视频读取完成，共处理 {self._current_frame} 帧")
                    break

                objects = self.detect_objects(frame)
                if objects:
                    last_objects = objects
                elif last_objects and self._current_frame % (skip_frames + 1) != 0:
                    objects = last_objects
                    logger.debug(f"帧 {self._current_frame}: 使用上一帧检测结果，共 {len(last_objects)} 个目标")

                for (x, y, w, h, label) in objects:
                    if x + w <= frame.shape[1] and y + h <= frame.shape[0]:
                        roi = frame[y:y + h, x:x + w]
                        
                        # 根据检测类型选择不同的处理方法
                        if label.startswith("敏感文字_"):
                            processed_roi = self.apply_text_desensitization(roi, text_desensitization_method)
                            logger.debug(f"帧 {self._current_frame}: 对 {label} 应用文字脱敏({text_desensitization_method}) - 位置: ({x}, {y}), 大小: ({w}, {h})")
                        else:
                            processed_roi = self.apply_mosaic(roi, mosaic_intensity)
                            logger.debug(f"帧 {self._current_frame}: 对 {label} 应用马赛克 - 位置: ({x}, {y}), 大小: ({w}, {h})")
                        
                        frame[y:y + h, x:x + w] = processed_roi
                    else:
                        logger.warning(f"检测目标超出边界: {label} at ({x},{y},{w},{h})")

                out.write(frame)

                if progress_callback and self._current_frame % 10 == 0:
                    progress = self.progress
                    progress_callback(progress)
                    logger.info(
                        f"帧 {self._current_frame}: 进度 {progress['progress'] * 100:.1f}%, 已用 {progress['elapsed']}s, 剩余 {progress['remaining']}s")

                self._current_frame += 1

            status = {"status": "success", "output_path": output_path,
                      "total_frames": self._current_frame, "time_used": round(time.time() - self._start_time, 2)}
            logger.info(f"处理完成: {status}")

        except Exception as e:
            status = {"status": "error", "message": str(e)}
            logger.error(f"视频处理异常: {str(e)}", exc_info=True)
        finally:
            if cap is not None:
                cap.release()
            if out is not None:
                out.release()
            self._processing = False
            if completion_callback:
                completion_callback(status)
            logger.info("资源清理完成")

    def stop_processing(self) -> None:
        self._processing = False
        logger.info("收到中止信号")

    def process_videos_batch(
            self,
            input_dir: str,
            output_dir: str,
            skip_frames: int = 0,
            mosaic_intensity: int = 5,
            text_desensitization_method: str = 'mosaic',
            progress_callback: Optional[Callable] = None,
            completion_callback: Optional[Callable] = None
    ) -> None:
        """
        批量处理目录中的所有视频文件
        :param input_dir: 输入目录路径
        :param output_dir: 输出目录路径
        :param skip_frames: 跳帧数
        :param mosaic_intensity: 马赛克强度
        :param text_desensitization_method: 文字脱敏方法
        :param progress_callback: 进度回调函数
        :param completion_callback: 完成回调函数
        """
        input_path = Path(input_dir)
        output_path = Path(output_dir)

        if not input_path.exists() or not input_path.is_dir():
            raise ValueError(f"输入目录不存在或不是目录: {input_dir}")

        # 创建输出目录
        output_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"输出目录: {output_path}")

        # 支持的视频文件扩展名（只使用小写，glob会自动匹配大小写）
        video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.3gp', '.m4v'}
        
        # 查找所有视频文件（使用集合去重，避免重复文件）
        video_files = set()
        for ext in video_extensions:
            # 同时匹配小写和大写扩展名
            video_files.update(input_path.glob(f'*{ext}'))
            video_files.update(input_path.glob(f'*{ext.upper()}'))
        
        # 转换为列表并按文件名排序
        video_files = sorted(list(video_files))

        if not video_files:
            logger.warning(f"输入目录 {input_dir} 中未找到视频文件")
            return

        logger.info(f"发现 {len(video_files)} 个视频文件待处理")
        
        successful_count = 0
        failed_count = 0
        
        for i, video_file in enumerate(video_files, 1):
            try:
                # 生成输出文件名（保持原扩展名，但统一输出为mp4）
                output_file = output_path / f"{video_file.stem}_processed.mp4"
                
                logger.info(f"[{i}/{len(video_files)}] 开始处理: {video_file.name}")
                
                # 为批量处理创建单独的进度回调
                def batch_progress_callback(progress: dict):
                    if progress_callback:
                        # 添加批量处理的额外信息
                        batch_progress = {
                            **progress,
                            'current_file': i,
                            'total_files': len(video_files),
                            'current_filename': video_file.name,
                            'overall_progress': (i - 1 + progress['progress']) / len(video_files)
                        }
                        progress_callback(batch_progress)
                
                # 为批量处理创建单独的完成回调
                def batch_completion_callback(result: dict):
                    nonlocal successful_count, failed_count
                    if result["status"] == "success":
                        successful_count += 1
                        logger.info(f"✅ [{i}/{len(video_files)}] 处理成功: {video_file.name} -> {output_file.name}")
                    else:
                        failed_count += 1
                        logger.error(f"❌ [{i}/{len(video_files)}] 处理失败: {video_file.name} - {result.get('message', '未知错误')}")
                
                # 处理单个视频
                self.process_video(
                    input_path=str(video_file),
                    output_path=str(output_file),
                    skip_frames=skip_frames,
                    mosaic_intensity=mosaic_intensity,
                    text_desensitization_method=text_desensitization_method,
                    progress_callback=batch_progress_callback,
                    completion_callback=batch_completion_callback
                )
                
            except Exception as e:
                failed_count += 1
                logger.error(f"❌ [{i}/{len(video_files)}] 处理异常: {video_file.name} - {str(e)}", exc_info=True)
        
        # 批量处理完成总结
        total_processed = successful_count + failed_count
        batch_result = {
            "status": "completed",
            "total_files": len(video_files),
            "successful": successful_count,
            "failed": failed_count,
            "success_rate": f"{(successful_count / total_processed * 100):.1f}%" if total_processed > 0 else "0%",
            "output_dir": str(output_path)
        }
        
        logger.info(f"🎉 批量处理完成！")
        logger.info(f"📊 处理统计: 总计 {len(video_files)} 个文件, 成功 {successful_count} 个, 失败 {failed_count} 个")
        logger.info(f"📁 输出目录: {output_path}")
        
        if completion_callback:
            completion_callback(batch_result)


if __name__ == "__main__":
    def print_progress(progress: dict):
        print(
            f"\r处理进度: {progress['progress'] * 100:.1f}% | 已用: {progress['elapsed']}s | 剩余: {progress['remaining']}s",
            end="")


    def on_complete(result: dict):
        print("\n" + "-" * 50)
        if result["status"] == "success":
            print(f"处理完成！输出: {result['output_path']}")
            print(f"总帧数: {result['total_frames']} 耗时: {result['time_used']}s")
        else:
            print(f"处理失败: {result['message']}")


    parser = argparse.ArgumentParser(description='增强版视频马赛克工具 + 证照敏感信息脱敏')
    parser.add_argument('-i', '--input', required=True, help="输入视频路径或目录")
    parser.add_argument('-o', '--output', required=True, help="输出视频路径或目录")
    parser.add_argument('--yolo_models', type=str, action='append', help="YOLOv8 模型路径，可多次指定")
    parser.add_argument('--skip_frames', type=int, default=0, help="跳帧数")
    parser.add_argument('--intensity', type=int, default=5, help="马赛克强度（越小越模糊）")
    parser.add_argument('--no_yolo', action='store_false', dest='enable_yolo', help="禁用 YOLO 检测")
    parser.add_argument('--enable_ocr', action='store_true', help="启用 OCR 敏感文字检测")
    parser.add_argument('--ocr_mode', type=str, default='all', 
                        choices=['id_card', 'bank_card', 'driver_license', 'phone', 'email', 'all'],
                        help="OCR 检测模式：id_card(身份证), bank_card(银行卡), driver_license(驾驶证), phone(手机号), email(邮箱), all(全部)")
    parser.add_argument('--text_method', type=str, default='mosaic',
                        choices=['mosaic', 'black_bar', 'blur'],
                        help="文字脱敏方法：mosaic(马赛克), black_bar(黑条), blur(模糊)")
    parser.add_argument('--ocr_interval', type=int, default=30,
                        help="OCR检测帧间隔（每N帧检测一次，提高性能）")
    parser.add_argument('--batch', action='store_true', help="启用批量处理模式（输入输出为目录）")
    args = parser.parse_args()

    # 验证参数
    if not args.enable_yolo and not args.enable_ocr:
        logger.error("必须启用 YOLO 检测或 OCR 检测中的至少一个功能")
        sys.exit(1)
    
    if args.enable_yolo and not args.yolo_models:
        logger.error("启用 YOLO 检测时必须提供模型路径")
        sys.exit(1)

    try:
        processor = VideoProcessor(
            yolo_model_paths=args.yolo_models or [],
            enable_yolo=args.enable_yolo,
            enable_ocr=args.enable_ocr,
            ocr_mode=args.ocr_mode,
            ocr_frame_interval=args.ocr_interval
        )
        
        if args.batch:
            # 批量处理模式
            def batch_print_progress(progress: dict):
                if 'current_file' in progress:
                    print(f"\r[{progress['current_file']}/{progress['total_files']}] {progress['current_filename']} | "
                          f"文件进度: {progress['progress'] * 100:.1f}% | 总进度: {progress['overall_progress'] * 100:.1f}% | "
                          f"已用: {progress['elapsed']}s | 剩余: {progress['remaining']}s", end="")
                else:
                    print_progress(progress)

            def batch_on_complete(result: dict):
                print("\n" + "=" * 60)
                if result["status"] == "completed":
                    print(f"🎉 批量处理完成！")
                    print(f"📊 处理统计:")
                    print(f"   总文件数: {result['total_files']}")
                    print(f"   成功处理: {result['successful']}")
                    print(f"   处理失败: {result['failed']}")
                    print(f"   成功率: {result['success_rate']}")
                    print(f"📁 输出目录: {result['output_dir']}")
                else:
                    print(f"❌ 批量处理失败: {result.get('message', '未知错误')}")

            processor.process_videos_batch(
                input_dir=args.input,
                output_dir=args.output,
                skip_frames=max(0, args.skip_frames),
                mosaic_intensity=args.intensity,
                text_desensitization_method=args.text_method,
                progress_callback=batch_print_progress,
                completion_callback=batch_on_complete
            )
        else:
            # 单文件处理模式
            processor.process_video(
                input_path=args.input,
                output_path=args.output,
                skip_frames=max(0, args.skip_frames),
                mosaic_intensity=args.intensity,
                text_desensitization_method=args.text_method,
                progress_callback=print_progress,
                completion_callback=on_complete
            )
    except Exception as e:
        logger.error(f"程序启动失败: {str(e)}", exc_info=True)
        sys.exit(1)
