# 构建阶段
FROM python:3.10-slim AS builder

# 设置工作目录
WORKDIR /app

# 设置国内 PyPI 镜像源，并禁用默认源
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple && \
    pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn

# 完全覆盖 Debian 源为阿里云源，并清理可能存在的其他源
RUN rm -rf /etc/apt/sources.list.d/* && \
    rm -f /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/debian/ bookworm main contrib non-free" > /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/debian/ bookworm-updates main contrib non-free" >> /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/debian-security/ bookworm-security main contrib non-free" >> /etc/apt/sources.list && \
    cat /etc/apt/sources.list

# 安装构建依赖，使用 --no-install-recommends 减少不必要的包
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libopencv-dev \
    && rm -rf /var/lib/apt/lists/*

# 复制 requirements.txt 并安装 Python 依赖
COPY requirements.txt .
RUN pip install --no-cache-dir --timeout=1200 --retries=3 -r requirements.txt

# 运行阶段
FROM python:3.10-slim

# 设置工作目录
WORKDIR /app

# 完全覆盖 Debian 源为阿里云源，并清理可能存在的其他源
RUN rm -rf /etc/apt/sources.list.d/* && \
    rm -f /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/debian/ bookworm main contrib non-free" > /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/debian/ bookworm-updates main contrib non-free" >> /etc/apt/sources.list && \
    echo "deb http://mirrors.aliyun.com/debian-security/ bookworm-security main contrib non-free" >> /etc/apt/sources.list && \
    cat /etc/apt/sources.list

# 安装运行时依赖，使用 --no-install-recommends 减少不必要的包
RUN apt-get update && apt-get install -y --no-install-recommends \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    ffmpeg \
    tzdata \
    && rm -rf /var/lib/apt/lists/*

# 设置时区为东八区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 从构建阶段复制已安装的依赖
COPY --from=builder /usr/local/lib/python3.10/site-packages/ /usr/local/lib/python3.10/site-packages/

# 复制项目文件
COPY mosaic_image_paddle.py mosaic_video_paddle.py ./
COPY run.sh ./
COPY download/ ./download/
COPY models/ ./models/

# 确保 run.sh 可执行并创建目录
RUN chmod +x run.sh && \
    sed -i 's/\r$//' run.sh && \
    mkdir -p /app/input /app/output /app/data /app/models

# 设置入口点
ENTRYPOINT ["./run.sh"]